# -*- coding: utf-8 -*-
"""
Simple test for force active functionality
"""

import sqlite3
import datetime
import os

def test_database_operations():
    """Test basic database operations for force active mode"""
    print("Testing force active mode database operations...")

    # Database configuration
    DATABASE_PATH = "gytrading2.db"
    AUTO_CALCULATE = True  # Test auto calculation mode
    FORCE_ACTIVE_START_DATE = "2024-06-28"  # Fallback values
    FORCE_ACTIVE_START_PRICE = 1.234
    
    try:
        # Connect to database
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        print("✓ Database connection successful")
        
        # Test 1: Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        required_tables = ['strategy_status', 'signal_history']
        for table in required_tables:
            if table in table_names:
                print(f"✓ Table {table} exists")
            else:
                print(f"✗ Table {table} missing")
                return False
        
        # Test 2: Check current strategy status
        cursor.execute("SELECT current_phase FROM strategy_status ORDER BY id DESC LIMIT 1")
        result = cursor.fetchone()
        if result:
            current_phase = result[0]
            print(f"✓ Current strategy phase: {current_phase}")
        else:
            print("✗ No strategy status found")
            return False
        
        # Test 3: Test auto calculation or manual signal insertion
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if AUTO_CALCULATE:
            print("✓ Testing auto calculation mode")
            # Simulate auto calculation result
            activation_info = {
                'date': '2024-06-28',
                'price': 1.234,
                'ema': 1.452,
                'bottom_line': 1.234,
                'source': 'auto_calculated_test'
            }
            signal_date = activation_info['date']
            signal_price = activation_info['price']
            signal_source = activation_info['source']
        else:
            print("✓ Testing manual mode")
            activation_info = {
                'date': FORCE_ACTIVE_START_DATE,
                'price': FORCE_ACTIVE_START_PRICE,
                'source': 'manual_test'
            }
            signal_date = FORCE_ACTIVE_START_DATE
            signal_price = FORCE_ACTIVE_START_PRICE
            signal_source = 'manual_test'

        signal_time = signal_date + " 15:00:00"

        # Check if signal already exists
        cursor.execute("""
            SELECT COUNT(*) FROM signal_history
            WHERE signal_type = 'ENTERLONG'
            AND DATE(signal_date) = ?
            AND is_valid = 1
        """, (signal_date,))

        existing_count = cursor.fetchone()[0]
        if existing_count > 0:
            print(f"✓ Simulated signal already exists for {signal_date}")
        else:
            # Insert simulated signal
            if 'ema' in activation_info:
                simulated_ema = activation_info['ema']
                simulated_bottom_line = activation_info['bottom_line']
            else:
                simulated_ema = signal_price / 0.85  # BOTTOM_RATIO = 0.85
                simulated_bottom_line = simulated_ema * 0.85

            cursor.execute("""
                INSERT INTO signal_history
                (signal_date, signal_type, signal_price, ema_value, bottom_line, top_line,
                 kline_position, kline_date, is_valid, filter_reason, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                signal_time,
                'ENTERLONG',
                signal_price,
                simulated_ema,
                simulated_bottom_line,
                None,  # top_line
                9999,  # kline_position
                signal_date.replace('-', ''),  # kline_date
                1,     # is_valid
                f"Test signal ({signal_source})",  # filter_reason
                current_time
            ))

            conn.commit()
            print(f"✓ Simulated buy signal inserted for {signal_time} (source: {signal_source})")
        
        # Test 4: Update strategy status to active
        cursor.execute("""
            UPDATE strategy_status SET
            current_phase = 'active',
            first_activation_time = ?,
            start_period_date = ?,
            start_period_price = ?,
            current_period = 0,
            last_check_time = ?,
            updated_time = ?
            WHERE id = (SELECT MAX(id) FROM strategy_status)
        """, (
            signal_time,
            signal_date,
            signal_price,
            current_time,
            current_time
        ))
        
        conn.commit()
        print("✓ Strategy status updated to active")
        
        # Test 5: Verify final status
        cursor.execute("""
            SELECT current_phase, start_period_date, start_period_price, first_activation_time
            FROM strategy_status ORDER BY id DESC LIMIT 1
        """)
        result = cursor.fetchone()
        if result:
            phase, start_date, start_price, activation_time = result
            print(f"✓ Final verification:")
            print(f"  Phase: {phase}")
            print(f"  Start date: {start_date}")
            print(f"  Start price: {start_price}")
            print(f"  Activation time: {activation_time}")
            print(f"  Calculation mode: {'Auto' if AUTO_CALCULATE else 'Manual'}")
        
        # Test 6: Check signal history
        cursor.execute("""
            SELECT signal_date, signal_type, signal_price, is_valid 
            FROM signal_history 
            WHERE signal_type = 'ENTERLONG' AND is_valid = 1
            ORDER BY signal_date DESC 
            LIMIT 3
        """)
        
        signals = cursor.fetchall()
        if signals:
            print("✓ Recent buy signals:")
            for signal in signals:
                print(f"  {signal[0]} - {signal[1]} - Price: {signal[2]:.4f}")
        
        conn.close()
        print("\n🎉 All tests passed! Force active mode is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return False


def show_usage_guide():
    """Show usage guide for force active mode"""
    print("\n" + "=" * 60)
    print("FORCE ACTIVE MODE USAGE GUIDE")
    print("=" * 60)
    
    print("\nTo enable force active mode for client deployment:")
    print("\n1. Edit value_averaging_strategy.py:")
    print("   FORCE_ACTIVE_MODE = True")
    print("   AUTO_CALCULATE_ACTIVATION = True  # Recommended: auto calculate")
    print("   HISTORICAL_ANALYSIS_YEARS = 3    # Years of data to analyze")
    print("")
    print("   Alternative (manual mode):")
    print("   AUTO_CALCULATE_ACTIVATION = False")
    print("   FORCE_ACTIVE_START_DATE = '2024-06-28'")
    print("   FORCE_ACTIVE_START_PRICE = 1.234")

    print("\n2. Start the strategy normally")
    print("   The system will automatically:")
    print("   - Analyze historical data to find latest buy signal (auto mode)")
    print("   - Or use manually set date and price (manual mode)")
    print("   - Insert simulated buy signal")
    print("   - Set strategy status to active phase")
    print("   - Begin value averaging strategy execution")
    
    print("\n3. Verify activation in logs:")
    print("   Look for: '✓ Strategy force activation successful'")
    print("   Current strategy status: active")
    print("   Force activation mode: enabled")
    
    print("\n⚠️  Important Notes:")
    print("   - Auto calculation mode analyzes historical data to find optimal activation point")
    print("   - Manual mode uses fixed date and price (less flexible)")
    print("   - Force activation only works when transitioning from sleeping to active")
    print("   - If strategy is already active, it will skip activation")
    print("   - Test in development environment before production deployment")
    print("   - Ensure sufficient funds for value averaging strategy")

    print("\n✅ Advantages of Auto Calculation Mode:")
    print("   - No need to manually update dates and prices")
    print("   - Automatically finds the most recent valid buy signal")
    print("   - Uses real iQuant API to get historical data")
    print("   - Adapts to different market conditions")
    print("   - More robust and maintenance-free")
    print("   - No more simulated data - uses actual market data!")


def main():
    """Main function"""
    print("Force Active Mode Test Script")
    print("Testing database operations for force activation")
    
    choice = input("\nChoose an option:\n1. Run database test\n2. Show usage guide\n3. Both\nEnter choice (1-3): ").strip()
    
    if choice in ['1', '3']:
        print("\n" + "=" * 60)
        print("DATABASE OPERATIONS TEST")
        print("=" * 60)
        success = test_database_operations()
        
        if not success:
            print("\nPlease check the error messages and fix any issues.")
    
    if choice in ['2', '3']:
        show_usage_guide()
    
    print("\nTest completed.")


if __name__ == "__main__":
    main()
