# -*- coding: utf-8 -*-

"""
测试日志记录修复效果
"""

import sqlite3
import datetime
import uuid
import json

def test_new_async_buy_system():
    """测试新的异步买入系统的日志记录"""
    print("🧪 测试新的异步买入系统日志记录")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_data.db')
        cursor = conn.cursor()
        
        # 清理测试数据
        test_task_group_id = str(uuid.uuid4())
        cursor.execute("DELETE FROM trade_task_queue WHERE task_group_id = ?", (test_task_group_id,))
        cursor.execute("DELETE FROM trade_orders WHERE order_reason = 'TEST_VALUE_AVERAGE'")
        cursor.execute("DELETE FROM trade_logs WHERE operation LIKE '%TEST%'")
        conn.commit()
        
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 1. 模拟创建买入任务
        print("📝 步骤1：创建买入任务")
        cursor.execute("""
            INSERT INTO trade_task_queue 
            (task_group_id, task_type, stock_code, target_shares, target_amount, 
             estimated_price, estimated_fees, task_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (test_task_group_id, "BUY_159915_CASH", "159915.SZ", 1000, 2500.0, 
              2.5, 5.0, "PENDING", str(uuid.uuid4()), current_time))
        
        task_id = cursor.lastrowid
        
        # 2. 模拟创建对应的 trade_orders 记录
        print("📝 步骤2：创建订单记录")
        cursor.execute("""
            INSERT INTO trade_orders 
            (order_date, stock_code, order_type, order_reason, target_shares, 
             order_status, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_VALUE_AVERAGE", 1000, 
              "PENDING", current_time))
        
        order_record_id = cursor.lastrowid
        
        # 3. 模拟下单成功（旧系统的日志）
        print("📝 步骤3：模拟下单成功日志")
        cursor.execute("""
            INSERT INTO trade_logs (kline_date, log_type, operation, message, details, created_time)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (current_time, "INFO", "交易执行",
              "[实盘交易] 下单成功：BUY 159915.SZ 1000股，原因：TEST_VALUE_AVERAGE，等待成交确认",
              None, current_time))
        
        # 4. 模拟任务成功完成
        print("📝 步骤4：模拟任务成功完成")
        cursor.execute("""
            UPDATE trade_task_queue 
            SET task_status = 'COMPLETED', completed_time = ?
            WHERE id = ?
        """, (current_time, task_id))
        
        # 5. 模拟状态同步（成功）
        print("📝 步骤5：模拟状态同步（成功）")
        cursor.execute("""
            UPDATE trade_orders 
            SET order_status = 'SUCCESS', actual_shares = 1000, actual_price = 2.5, execution_time = ?
            WHERE id = ?
        """, (current_time, order_record_id))
        
        # 6. 模拟记录交易成功日志
        print("📝 步骤6：记录交易成功日志")
        cursor.execute("""
            INSERT INTO trade_logs (kline_date, log_type, operation, message, details, created_time)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (current_time, "INFO", "交易执行",
              "[实盘交易] 交易成功：BUY 159915.SZ 1000股，原因：VALUE_AVERAGE",
              None, current_time))
        
        conn.commit()
        
        # 验证结果
        print("\n📊 验证结果：")
        
        # 检查任务状态
        cursor.execute("SELECT task_status FROM trade_task_queue WHERE id = ?", (task_id,))
        task_status = cursor.fetchone()[0]
        print(f"   任务状态：{task_status} (期望: COMPLETED)")
        
        # 检查订单状态
        cursor.execute("SELECT order_status, actual_shares FROM trade_orders WHERE id = ?", (order_record_id,))
        order_result = cursor.fetchone()
        order_status, actual_shares = order_result
        print(f"   订单状态：{order_status} (期望: SUCCESS)")
        print(f"   实际股数：{actual_shares} (期望: 1000)")
        
        # 检查日志记录
        cursor.execute("""
            SELECT COUNT(*) FROM trade_logs 
            WHERE operation = '交易执行' AND message LIKE '%下单成功%TEST_VALUE_AVERAGE%'
        """)
        submit_logs = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM trade_logs 
            WHERE operation = '交易执行' AND message LIKE '%交易成功%VALUE_AVERAGE%'
        """)
        success_logs = cursor.fetchone()[0]
        
        print(f"   下单成功日志：{submit_logs} (期望: 1)")
        print(f"   交易成功日志：{success_logs} (期望: 1)")
        
        # 清理测试数据
        cursor.execute("DELETE FROM trade_task_queue WHERE task_group_id = ?", (test_task_group_id,))
        cursor.execute("DELETE FROM trade_orders WHERE id = ?", (order_record_id,))
        cursor.execute("DELETE FROM trade_logs WHERE operation LIKE '%TEST%' OR message LIKE '%TEST%'")
        conn.commit()
        conn.close()
        
        # 判断测试结果
        success = (task_status == 'COMPLETED' and 
                  order_status == 'SUCCESS' and 
                  actual_shares == 1000 and
                  submit_logs == 1 and 
                  success_logs == 1)
        
        if success:
            print("✅ 新的异步买入系统日志记录测试通过")
            return True
        else:
            print("❌ 新的异步买入系统日志记录测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_failed_order_logging():
    """测试废单的日志记录"""
    print("\n🧪 测试废单的日志记录")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_data.db')
        cursor = conn.cursor()
        
        # 清理测试数据
        test_task_group_id = str(uuid.uuid4())
        cursor.execute("DELETE FROM trade_task_queue WHERE task_group_id = ?", (test_task_group_id,))
        cursor.execute("DELETE FROM trade_orders WHERE order_reason = 'TEST_FAILED_ORDER'")
        cursor.execute("DELETE FROM trade_logs WHERE message LIKE '%TEST_FAILED%'")
        conn.commit()
        
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 1. 创建买入任务
        cursor.execute("""
            INSERT INTO trade_task_queue 
            (task_group_id, task_type, stock_code, target_shares, target_amount, 
             estimated_price, estimated_fees, task_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (test_task_group_id, "BUY_159915_CASH", "159915.SZ", 1000, 2500.0, 
              2.5, 5.0, "PENDING", str(uuid.uuid4()), current_time))
        
        task_id = cursor.lastrowid
        
        # 2. 创建对应的 trade_orders 记录
        cursor.execute("""
            INSERT INTO trade_orders 
            (order_date, stock_code, order_type, order_reason, target_shares, 
             order_status, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_FAILED_ORDER", 1000, 
              "PENDING", current_time))
        
        order_record_id = cursor.lastrowid
        
        # 3. 模拟下单成功日志
        cursor.execute("""
            INSERT INTO trade_logs (kline_date, log_type, operation, message, details, created_time)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (current_time, "INFO", "交易执行",
              "[实盘交易] 下单成功：BUY 159915.SZ 1000股，原因：TEST_FAILED_ORDER，等待成交确认",
              None, current_time))
        
        # 4. 模拟任务失败
        cursor.execute("""
            UPDATE trade_task_queue 
            SET task_status = 'FAILED', error_message = '订单废单', completed_time = ?
            WHERE id = ?
        """, (current_time, task_id))
        
        # 5. 模拟状态同步（失败）
        cursor.execute("""
            UPDATE trade_orders 
            SET order_status = 'FAILED', actual_shares = 0, actual_price = 0, 
                error_message = '订单废单', execution_time = ?
            WHERE id = ?
        """, (current_time, order_record_id))
        
        # 6. 模拟记录交易失败日志
        cursor.execute("""
            INSERT INTO trade_logs (kline_date, log_type, operation, message, details, created_time)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (current_time, "ERROR", "交易执行",
              "[实盘交易] 交易失败：BUY 159915.SZ 1000股，原因：VALUE_AVERAGE，错误：订单失败：废单",
              None, current_time))
        
        conn.commit()
        
        # 验证结果
        print("📊 验证结果：")
        
        # 检查任务状态
        cursor.execute("SELECT task_status, error_message FROM trade_task_queue WHERE id = ?", (task_id,))
        task_result = cursor.fetchone()
        task_status, error_message = task_result
        print(f"   任务状态：{task_status} (期望: FAILED)")
        print(f"   错误信息：{error_message} (期望: 订单废单)")
        
        # 检查订单状态
        cursor.execute("SELECT order_status, actual_shares, error_message FROM trade_orders WHERE id = ?", (order_record_id,))
        order_result = cursor.fetchone()
        order_status, actual_shares, order_error = order_result
        print(f"   订单状态：{order_status} (期望: FAILED)")
        print(f"   实际股数：{actual_shares} (期望: 0)")
        print(f"   订单错误：{order_error} (期望: 订单废单)")
        
        # 检查日志记录
        cursor.execute("""
            SELECT COUNT(*) FROM trade_logs 
            WHERE operation = '交易执行' AND log_type = 'ERROR' AND message LIKE '%交易失败%'
        """)
        failed_logs = cursor.fetchone()[0]
        print(f"   交易失败日志：{failed_logs} (期望: 1)")
        
        # 清理测试数据
        cursor.execute("DELETE FROM trade_task_queue WHERE task_group_id = ?", (test_task_group_id,))
        cursor.execute("DELETE FROM trade_orders WHERE id = ?", (order_record_id,))
        cursor.execute("DELETE FROM trade_logs WHERE message LIKE '%TEST_FAILED%'")
        conn.commit()
        conn.close()
        
        # 判断测试结果
        success = (task_status == 'FAILED' and 
                  order_status == 'FAILED' and 
                  actual_shares == 0 and
                  failed_logs == 1)
        
        if success:
            print("✅ 废单日志记录测试通过")
            return True
        else:
            print("❌ 废单日志记录测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试日志记录修复效果\n")
    
    # 测试1：新的异步买入系统
    test1_result = test_new_async_buy_system()
    
    # 测试2：废单日志记录
    test2_result = test_failed_order_logging()
    
    # 总结
    print(f"\n📋 测试总结：")
    print(f"   异步买入系统日志：{'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   废单日志记录：{'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！日志记录修复效果良好。")
        print("\n✨ 修复要点：")
        print("   1. 下单成功 ≠ 交易成功，日志区分明确")
        print("   2. 只有在回调确认后才记录'交易成功'")
        print("   3. 废单正确记录为'交易失败'")
        print("   4. trade_orders 表状态与日志保持一致")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
