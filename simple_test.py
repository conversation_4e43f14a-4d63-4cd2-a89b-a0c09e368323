# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

print("开始测试...")

try:
    import sqlite3
    print("✅ sqlite3 导入成功")
except ImportError as e:
    print(f"❌ sqlite3 导入失败: {e}")

try:
    import datetime
    print("✅ datetime 导入成功")
except ImportError as e:
    print(f"❌ datetime 导入失败: {e}")

try:
    import json
    print("✅ json 导入成功")
except ImportError as e:
    print(f"❌ json 导入失败: {e}")

try:
    import math
    print("✅ math 导入成功")
except ImportError as e:
    print(f"❌ math 导入失败: {e}")

# 测试基本的数据库操作
try:
    conn = sqlite3.connect(":memory:")
    cursor = conn.cursor()
    cursor.execute("CREATE TABLE test (id INTEGER, name TEXT)")
    cursor.execute("INSERT INTO test VALUES (1, 'test')")
    result = cursor.fetchone()
    conn.close()
    print("✅ 数据库操作测试成功")
except Exception as e:
    print(f"❌ 数据库操作测试失败: {e}")

print("测试完成")
