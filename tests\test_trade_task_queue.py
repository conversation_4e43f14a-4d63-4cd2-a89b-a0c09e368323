#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易任务队列系统
验证异步交易任务的创建、执行和回调处理
"""

import sqlite3
import datetime
import os
import sys
import json
import uuid

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_database():
    """创建测试数据库"""
    if os.path.exists('test_trade_task_queue.db'):
        os.remove('test_trade_task_queue.db')
    
    conn = sqlite3.connect('test_trade_task_queue.db')
    cursor = conn.cursor()
    
    # 创建交易任务队列表
    cursor.execute("""
        CREATE TABLE trade_task_queue (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_group_id TEXT NOT NULL,
            task_type TEXT NOT NULL,
            stock_code TEXT NOT NULL,
            target_shares INTEGER NOT NULL,
            target_amount REAL,
            estimated_price REAL,
            estimated_fees REAL,
            task_status TEXT NOT NULL,
            depends_on_task TEXT,
            order_id TEXT,
            task_params TEXT,
            created_time TEXT NOT NULL,
            started_time TEXT,
            completed_time TEXT,
            error_message TEXT,
            warning_logged INTEGER DEFAULT 0,
            status_queried INTEGER DEFAULT 0,
            alert_sent INTEGER DEFAULT 0
        )
    """)
    

    
    # 创建交易任务日志表（合并了原 trade_task_execution 表的功能）
    cursor.execute("""
        CREATE TABLE trade_task_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id INTEGER,
            task_group_id TEXT,
            log_level TEXT NOT NULL,
            log_category TEXT NOT NULL,
            log_message TEXT NOT NULL,
            extra_data TEXT,
            log_time TEXT NOT NULL,
            -- 以下字段来自原 trade_task_execution 表
            execution_step TEXT,
            step_status TEXT,
            actual_shares INTEGER,
            actual_price REAL,
            actual_amount REAL,
            actual_fees REAL,
            callback_data TEXT
        )
    """)
    
    # 创建账户快照表
    cursor.execute("""
        CREATE TABLE account_snapshot (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_group_id TEXT NOT NULL,
            snapshot_point TEXT NOT NULL,
            available_cash REAL,
            margin_available REAL,
            stock_510720_shares INTEGER,
            stock_510720_value REAL,
            stock_159915_shares INTEGER,
            stock_159915_value REAL,
            snapshot_time TEXT NOT NULL
        )
    """)
    
    conn.commit()
    return conn

def test_task_creation():
    """测试任务创建"""
    print("=== 测试任务创建 ===")
    
    conn = create_test_database()
    cursor = conn.cursor()
    
    # 模拟创建任务组
    task_group_id = str(uuid.uuid4())
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 创建卖出任务
    cursor.execute("""
        INSERT INTO trade_task_queue
        (task_group_id, task_type, stock_code, target_shares, target_amount,
         estimated_price, estimated_fees, task_status, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        task_group_id, 'SELL_510720', '510720', 1000, 2500.0,
        2.5, 7.5, 'PENDING', current_time
    ))
    
    sell_task_id = cursor.lastrowid
    
    # 创建现金买入任务
    cursor.execute("""
        INSERT INTO trade_task_queue
        (task_group_id, task_type, stock_code, target_shares, target_amount,
         estimated_price, estimated_fees, task_status, depends_on_task, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        task_group_id, 'BUY_159915_CASH', '159915', 2000, 2460.0,
        1.23, 7.38, 'PENDING', str(sell_task_id), current_time
    ))
    
    buy_cash_task_id = cursor.lastrowid
    
    # 创建融资买入任务
    cursor.execute("""
        INSERT INTO trade_task_queue
        (task_group_id, task_type, stock_code, target_shares, target_amount,
         estimated_price, estimated_fees, task_status, depends_on_task, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        task_group_id, 'BUY_159915_MARGIN', '159915', 0, 0.0,
        1.23, 0.0, 'PENDING', str(buy_cash_task_id), current_time
    ))
    
    conn.commit()
    
    # 验证任务创建
    cursor.execute("SELECT COUNT(*) FROM trade_task_queue WHERE task_group_id = ?", (task_group_id,))
    task_count = cursor.fetchone()[0]
    
    assert task_count == 3, f"应该创建3个任务，实际创建{task_count}个"
    
    print(f"✅ 成功创建任务组：{task_group_id}")
    print(f"   - 卖出任务ID：{sell_task_id}")
    print(f"   - 现金买入任务ID：{buy_cash_task_id}")
    print(f"   - 融资买入任务ID：{cursor.lastrowid}")
    
    conn.close()
    if os.path.exists('test_trade_task_queue.db'):
        os.remove('test_trade_task_queue.db')

def test_task_dependency():
    """测试任务依赖关系"""
    print("\n=== 测试任务依赖关系 ===")
    
    conn = create_test_database()
    cursor = conn.cursor()
    
    # 创建测试任务
    task_group_id = str(uuid.uuid4())
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 任务1：卖出（无依赖）
    cursor.execute("""
        INSERT INTO trade_task_queue
        (task_group_id, task_type, stock_code, target_shares, task_status, created_time)
        VALUES (?, ?, ?, ?, ?, ?)
    """, (task_group_id, 'SELL_510720', '510720', 1000, 'PENDING', current_time))
    task1_id = cursor.lastrowid
    
    # 任务2：买入（依赖任务1）
    cursor.execute("""
        INSERT INTO trade_task_queue
        (task_group_id, task_type, stock_code, target_shares, task_status, depends_on_task, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """, (task_group_id, 'BUY_159915_CASH', '159915', 2000, 'PENDING', str(task1_id), current_time))
    task2_id = cursor.lastrowid
    
    conn.commit()
    
    # 测试查找可执行任务（任务1应该可执行）
    cursor.execute("""
        SELECT t1.id, t1.task_type FROM trade_task_queue t1
        WHERE t1.task_status = 'PENDING'
        AND (
            t1.depends_on_task IS NULL 
            OR EXISTS (
                SELECT 1 FROM trade_task_queue t2 
                WHERE t2.id = CAST(t1.depends_on_task AS INTEGER)
                AND t2.task_status = 'COMPLETED'
            )
        )
        ORDER BY t1.created_time ASC
    """)
    
    executable_tasks = cursor.fetchall()
    assert len(executable_tasks) == 1, f"应该有1个可执行任务，实际有{len(executable_tasks)}个"
    assert executable_tasks[0][0] == task1_id, "第一个可执行任务应该是卖出任务"
    
    print(f"✅ 依赖关系正确：任务{task1_id}可执行，任务{task2_id}等待依赖")
    
    # 完成任务1
    cursor.execute("UPDATE trade_task_queue SET task_status = 'COMPLETED' WHERE id = ?", (task1_id,))
    conn.commit()
    
    # 再次查找可执行任务（任务2应该可执行）
    cursor.execute("""
        SELECT t1.id, t1.task_type FROM trade_task_queue t1
        WHERE t1.task_status = 'PENDING'
        AND (
            t1.depends_on_task IS NULL 
            OR EXISTS (
                SELECT 1 FROM trade_task_queue t2 
                WHERE t2.id = CAST(t1.depends_on_task AS INTEGER)
                AND t2.task_status = 'COMPLETED'
            )
        )
        ORDER BY t1.created_time ASC
    """)
    
    executable_tasks = cursor.fetchall()
    assert len(executable_tasks) == 1, f"应该有1个可执行任务，实际有{len(executable_tasks)}个"
    assert executable_tasks[0][0] == task2_id, "现在可执行任务应该是买入任务"
    
    print(f"✅ 依赖解除正确：任务{task1_id}完成后，任务{task2_id}变为可执行")
    
    conn.close()
    if os.path.exists('test_trade_task_queue.db'):
        os.remove('test_trade_task_queue.db')

def test_callback_simulation():
    """测试回调处理模拟"""
    print("\n=== 测试回调处理模拟 ===")
    
    conn = create_test_database()
    cursor = conn.cursor()
    
    # 创建等待回调的任务
    task_group_id = str(uuid.uuid4())
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    cursor.execute("""
        INSERT INTO trade_task_queue
        (task_group_id, task_type, stock_code, target_shares, task_status, 
         order_id, started_time, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        task_group_id, 'SELL_510720', '510720', 1000, 'WAITING_CALLBACK',
        'ORDER123456', current_time, current_time
    ))
    
    task_id = cursor.lastrowid
    conn.commit()
    
    # 模拟订单回调
    callback_data = {
        'order_id': 'ORDER123456',
        'status': 56,  # 已成
        'deal_shares': 1000,
        'deal_price': 2.5,
        'deal_amount': 2500.0
    }
    
    # 记录回调数据到 trade_task_log 表
    cursor.execute("""
        INSERT INTO trade_task_log
        (task_id, task_group_id, log_level, log_category, log_message,
         log_time, execution_step, step_status, actual_shares, actual_price,
         actual_amount, callback_data)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (
        task_id, task_group_id, 'INFO', 'CALLBACK', '收到订单回调',
        current_time, 'ORDER_CALLBACK', 'RECEIVED',
        callback_data['deal_shares'], callback_data['deal_price'],
        callback_data['deal_amount'], json.dumps(callback_data)
    ))
    
    # 更新任务状态为完成
    cursor.execute("""
        UPDATE trade_task_queue 
        SET task_status = 'COMPLETED', completed_time = ?
        WHERE id = ?
    """, (current_time, task_id))
    
    conn.commit()
    
    # 验证回调处理结果
    cursor.execute("SELECT task_status FROM trade_task_queue WHERE id = ?", (task_id,))
    status = cursor.fetchone()[0]
    assert status == 'COMPLETED', f"任务状态应为COMPLETED，实际为{status}"
    
    cursor.execute("SELECT COUNT(*) FROM trade_task_execution WHERE task_id = ?", (task_id,))
    execution_count = cursor.fetchone()[0]
    assert execution_count == 1, f"应该有1条执行记录，实际有{execution_count}条"
    
    print(f"✅ 回调处理成功：任务{task_id}状态更新为COMPLETED")
    print(f"   - 成交股数：{callback_data['deal_shares']}")
    print(f"   - 成交价格：{callback_data['deal_price']}")
    print(f"   - 成交金额：{callback_data['deal_amount']}")
    
    conn.close()
    if os.path.exists('test_trade_task_queue.db'):
        os.remove('test_trade_task_queue.db')

if __name__ == "__main__":
    test_task_creation()
    test_task_dependency()
    test_callback_simulation()
    print("\n🎉 所有交易任务队列测试通过！")
