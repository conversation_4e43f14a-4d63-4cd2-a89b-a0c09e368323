# encoding:gbk
"""
激进版择时量化投资策略
基于创业板ETF(159915)技术指标进行择时，简化版本：
- 激活期：一次性买入固定金额的ACTIVE_FUND_CODE
- 沉睡期：全部卖出ACTIVE_FUND_CODE，买入等值的SLEEPING_FUND_CODE
- 每个期间只交易一次，避免重复操作
"""

import sqlite3
import datetime
import json
import math
import time
import numpy as np
from typing import Dict, List, Tuple, Optional
import traceback
import uuid

# ==================== 策略参数配置 ====================

#############################################################################
### 用户可以配置的参数 BEGIN
#############################################################################

# 基金代码配置
SLEEPING_FUND_CODE = "510720.SH"    # 沉睡期基金：国泰上证国有企业红利ETF
ACTIVE_FUND_CODE = "159967.SZ"      # 激活期基金：创业板ETF
SIGNAL_FUND_CODE = "159915.SZ"      # 信号检测基金：创业板ETF

# 激进版核心参数
AGGRESSIVE_INVESTMENT_AMOUNT = 100000  # 激进版固定投资金额（元）

# 技术指标参数(季线)
EMA_PERIOD = 35                 # EMA参数：默认35，可配置2-500
BOTTOM_RATIO = 0.85             # 底部相对比例：默认0.85
TOP_RATIO = 1.90                # 顶部相对比例：默认1.90

# 交易时点控制参数
TRADE_TIME_CONTROL = "144500"   # 交易时点控制：HHmmss格式，如144500代表14:45:00
ENABLE_TIME_CONTROL = True      # 是否启用时点控制（实盘模式有效，回测模式忽略）

# 交易账户信息
ACCOUNT_ID = "************"     # 设置交易账户信息（只能支持一个）
ACCOUNT_TYPE = "CREDIT"         # 交易账户类型，手动设置
COMMISSION_FEE_RATE = 0.0001   # 佣金费率（万分之1）
COMMISSION_FEE_MIN = 5         # 最低交易佣金（元）
SELL_TAX_RATE = 0.0005          # 印花税率（万分之5，仅卖出）
TRANSFER_FEE_RATE = 0.00002    # 过户费率（万分之0.2，仅上海）

#############################################################################
### 用户可以配置的参数 END
#############################################################################

# 技术指标参数
EMA_DETECTION_CYCLE = "1q"      # EMA检测周期：季(1q)

# 信号过滤参数
BUY_SIGNAL_FILTER_PERIODS = 8   # 买入信号过滤周期：8个周期内不重复
SELL_SIGNAL_FILTER_PERIODS = 10 # 卖出信号过滤周期：10个周期内不重复

# 交易执行参数
MAX_RETRY_COUNT = 3             # 最大重试次数
RETRY_INTERVAL = 300            # 重试间隔（秒）
MIN_TRADE_SHARES = 100          # 最小交易股数（必须是100的倍数）

# 数据库配置
DATABASE_PATH = "aggressive_strategy.db"   # 数据库文件路径

# 回测模式配置
IS_BACKTEST_MODE = True        # True: 回测模式（不真实下单）, False: 实盘模式

# ==================== 平台兼容性检查 ====================
# iQuant平台函数可用性检查
IQUANT_FUNCTIONS_AVAILABLE = False
try:
    # 检查是否在iQuant环境中
    passorder
    get_trade_detail_data
    IQUANT_FUNCTIONS_AVAILABLE = True
    print("iQuant平台函数可用")
except NameError:
    print("非iQuant环境，将使用模拟函数")
    IQUANT_FUNCTIONS_AVAILABLE = False

    # 定义模拟函数
    def passorder(*args, **kwargs):
        """模拟passorder函数"""
        print(f"[模拟] passorder调用: args={args}")
        return "MOCK_ORDER_ID"

    def get_trade_detail_data(*args, **kwargs):
        """模拟get_trade_detail_data函数"""
        print(f"[模拟] get_trade_detail_data调用: args={args}")
        return {}

# ==================== 全局变量 ====================
g_strategy_status = None        # 策略状态
g_db_connection = None          # 数据库连接
g_current_bar_time = None       # 当前K线时间（回测时使用）

# ==================== 回测适配函数 ====================

def is_backtest_mode(ContextInfo=None) -> bool:
    """判断是否为回测模式"""
    return IS_BACKTEST_MODE

def get_current_time(ContextInfo) -> datetime.datetime:
    """获取当前时间（回测适配）"""
    global g_current_bar_time
    
    try:
        if is_backtest_mode(ContextInfo):
            # 回测模式：使用当前K线的时间
            try:
                current_bar_timestamp = ContextInfo.get_bar_timetag(ContextInfo.barpos)
                if current_bar_timestamp:
                    g_current_bar_time = datetime.datetime.fromtimestamp(current_bar_timestamp/1000)
            except:
                print('获取K线时间失败，将会返回global的bartime')
                pass
            
            if g_current_bar_time:
                return g_current_bar_time
            else:
                return datetime.datetime.now()
        else:
            # 实盘模式：使用系统当前时间
            current_time = datetime.datetime.now()
            g_current_bar_time = current_time
            return current_time
            
    except Exception as e:
        log_message("WARNING", "时间获取", f"获取当前时间失败：{str(e)}", None, ContextInfo)
        current_time = datetime.datetime.now()
        g_current_bar_time = current_time
        return current_time

def get_current_time_str(ContextInfo) -> str:
    """获取当前时间字符串（回测适配）"""
    return get_current_time(ContextInfo).strftime("%Y-%m-%d %H:%M:%S")

def should_execute_strategy(ContextInfo) -> bool:
    """判断是否应该执行策略逻辑（回测适配）"""
    try:
        if is_backtest_mode(ContextInfo):
            return True
        else:
            return ContextInfo.is_last_bar()
    except Exception as e:
        log_message("WARNING", "策略执行判断", f"判断是否执行策略失败：{str(e)}", None, ContextInfo)
        return False

# ==================== 主要策略函数 ====================

def init(ContextInfo):
    """iQuant策略初始化函数"""
    try:
        print("=" * 60)
        print("激进版择时量化投资策略 - 初始化开始")
        print("=" * 60)

        ContextInfo.set_universe([ACTIVE_FUND_CODE, SLEEPING_FUND_CODE, SIGNAL_FUND_CODE])

        # 1. 初始化数据库
        print("正在初始化数据库...")
        init_database()
        print("数据库初始化完成！")

        # 2. 加载策略状态
        print("正在加载策略状态...")
        load_strategy_status()
        print("策略状态加载完成！")

        # 3. 设置账户信息
        ContextInfo.set_account(ACCOUNT_ID)
        print(f"已设置交易账户:{ACCOUNT_ID}")

        # 4. 显示策略配置信息
        print("\n激进版策略配置信息：")
        print(f"  沉睡期基金：{SLEEPING_FUND_CODE} (红利国企ETF)")
        print(f"  激活期基金：{ACTIVE_FUND_CODE} (创业板ETF)")
        print(f"  信号检测基金：{SIGNAL_FUND_CODE}")
        print(f"  固定投资金额：{AGGRESSIVE_INVESTMENT_AMOUNT:,}元")
        print(f"  EMA检测周期：{EMA_DETECTION_CYCLE}")
        print(f"  EMA参数：{EMA_PERIOD}")
        print(f"  底部比例：{BOTTOM_RATIO}")
        print(f"  顶部比例：{TOP_RATIO}")

        # 5. 下载历史数据
        current_date_str = datetime.datetime.now().strftime('%Y%m%d')
        print(f'即将下载 {SIGNAL_FUND_CODE} 的日线数据...')
        down_history_data(SIGNAL_FUND_CODE, '1d', '********', current_date_str)
        print(f'{SIGNAL_FUND_CODE} 的日线数据下载完成！')

        if ACTIVE_FUND_CODE != SIGNAL_FUND_CODE:
            print(f'即将下载 {ACTIVE_FUND_CODE} 的日线数据...')
            down_history_data(ACTIVE_FUND_CODE, '1d', '********', current_date_str)
            print(f'{ACTIVE_FUND_CODE} 的日线数据下载完成！')

        # 记录初始化日志
        log_message("INFO", "策略初始化", "激进版策略初始化成功", None, ContextInfo)

        print("\n" + "=" * 60)
        print("激进版择时策略初始化完成！")
        print("=" * 60)

    except Exception as e:
        error_msg = f"激进版策略初始化失败：{str(e)}"
        print(f"\n❌ {error_msg}")
        print("=" * 60)
        log_message("ERROR", "策略初始化", error_msg, None, ContextInfo)
        raise e

def handlebar(ContextInfo):
    """iQuant主策略逻辑函数"""
    try:
        # 判断是否应该执行策略逻辑（回测适配）
        if not should_execute_strategy(ContextInfo):
            return

        # 获取当前时间（回测适配）
        current_time = get_current_time_str(ContextInfo)

        if is_backtest_mode(ContextInfo):
            print(f"[激进版-回测模式] 执行策略，K线时间：{current_time}")
        else:
            print(f"[激进版-实盘模式] 当前为最后一根K线，执行策略，时间：{current_time}")
        
        # 检查账户是否已经登录
        account_data = get_account_info(ContextInfo)
        if not account_data or len(account_data) <= 0:
            log_message("ERROR", "策略运行", "当前账户未登录，请先登录！", None, ContextInfo)
            return

        # 执行激进版核心逻辑
        execute_aggressive_strategy(ContextInfo)

        # 记录运行日志
        log_message("INFO", "策略运行", f"激进版策略执行完成，时间：{current_time}", None, ContextInfo)

    except Exception as e:
        error_msg = f"激进版策略执行失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "策略执行", error_msg, None, ContextInfo)


# ==================== 激进版核心逻辑 ====================

def execute_aggressive_strategy(ContextInfo):
    """
    执行激进版策略核心逻辑

    Args:
        ContextInfo: iQuant上下文信息对象
    """
    try:
        # 1. 检测当前信号状态
        signal_result = detect_latest_signal(ContextInfo)
        if signal_result is None:
            log_message("WARNING", "激进版策略", "无法检测信号状态，跳过本次执行", None, ContextInfo)
            return

        current_phase = signal_result['current_phase']  # 'active' 或 'sleeping'
        latest_signal = signal_result['latest_signal']

        log_message("INFO", "激进版策略", f"当前阶段：{current_phase}，最新信号：{latest_signal['signal_type'] if latest_signal else 'None'}", None, ContextInfo)

        # 2. 检查本期间是否已经交易过
        if has_traded_in_current_period(current_phase, latest_signal):
            log_message("INFO", "激进版策略", f"本{current_phase}期间已经交易过，跳过", None, ContextInfo)
            return

        # 3. 根据当前阶段执行相应交易
        if current_phase == 'active':
            execute_active_phase_trade(ContextInfo, latest_signal)
        elif current_phase == 'sleeping':
            execute_sleeping_phase_trade(ContextInfo, latest_signal)

    except Exception as e:
        error_msg = f"激进版策略执行失败：{str(e)}"
        log_message("ERROR", "激进版策略", error_msg, None, ContextInfo)
        print(f"❌ {error_msg}")


def detect_latest_signal(ContextInfo):
    """
    检测最新的买卖信号，判断当前是激活期还是沉睡期

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 包含当前阶段和最新信号信息
    """
    try:
        # 获取技术指标数据
        technical_data = update_technical_indicators(ContextInfo)
        if technical_data is None:
            log_message("WARNING", "信号检测", "技术指标数据为空", None, ContextInfo)
            return None

        # 检测当前是否有新信号
        current_signal = check_current_signal(technical_data, ContextInfo)

        # 查询数据库中最近的信号记录
        latest_db_signal = get_latest_signal_from_db()

        # 如果有新信号，记录到数据库
        if current_signal and current_signal['has_signal']:
            record_signal_to_db(current_signal, ContextInfo)
            latest_signal = current_signal
        else:
            latest_signal = latest_db_signal

        # 根据最新信号判断当前阶段
        if latest_signal and latest_signal['signal_type'] == 'ENTERLONG':
            current_phase = 'active'
        elif latest_signal and latest_signal['signal_type'] == 'EXITLONG':
            current_phase = 'sleeping'
        else:
            # 如果没有信号记录，默认为沉睡期
            current_phase = 'sleeping'

        return {
            'current_phase': current_phase,
            'latest_signal': latest_signal,
            'has_new_signal': current_signal and current_signal['has_signal']
        }

    except Exception as e:
        log_message("ERROR", "信号检测", f"检测最新信号失败：{str(e)}", None, ContextInfo)
        return None


def execute_active_phase_trade(ContextInfo, latest_signal):
    """
    执行激活期交易：买入固定金额的ACTIVE_FUND_CODE

    Args:
        ContextInfo: iQuant上下文信息对象
        latest_signal: 最新信号信息
    """
    try:
        log_message("INFO", "激活期交易", f"开始执行激活期交易，目标金额：{AGGRESSIVE_INVESTMENT_AMOUNT:,}元", None, ContextInfo)

        # 获取当前价格
        current_price = get_current_price(ContextInfo, ACTIVE_FUND_CODE)
        if current_price is None:
            log_message("ERROR", "激活期交易", f"无法获取{ACTIVE_FUND_CODE}当前价格", None, ContextInfo)
            return

        # 计算需要买入的股数
        target_shares = math.floor(AGGRESSIVE_INVESTMENT_AMOUNT / current_price / 100) * 100
        if target_shares < MIN_TRADE_SHARES:
            log_message("WARNING", "激活期交易", f"计算的股数{target_shares}小于最小交易股数{MIN_TRADE_SHARES}", None, ContextInfo)
            return

        # 获取账户信息
        account_info = get_account_info(ContextInfo)
        if account_info is None:
            log_message("ERROR", "激活期交易", "无法获取账户信息", None, ContextInfo)
            return

        available_cash = account_info.get('available_cash', 0)
        margin_available = account_info.get('margin_available', 0)

        actual_amount = target_shares * current_price

        # 判断是否需要融资
        if available_cash >= actual_amount:
            # 现金充足，直接买入
            place_buy_order(ContextInfo, ACTIVE_FUND_CODE, target_shares, "现金买入")
        elif available_cash + margin_available >= actual_amount:
            # 需要融资
            cash_shares = math.floor(available_cash / current_price / 100) * 100
            margin_shares = target_shares - cash_shares

            # 先用现金买入
            if cash_shares >= MIN_TRADE_SHARES:
                place_buy_order(ContextInfo, ACTIVE_FUND_CODE, cash_shares, "现金买入")

            # 再用融资买入
            if margin_shares >= MIN_TRADE_SHARES:
                place_buy_order(ContextInfo, ACTIVE_FUND_CODE, margin_shares, "融资买入")
        else:
            log_message("ERROR", "激活期交易", f"资金不足，需要{actual_amount:,.2f}元，可用现金{available_cash:,.2f}元，融资额度{margin_available:,.2f}元", None, ContextInfo)
            return

        # 记录交易到数据库
        record_trade_to_db("BUY", ACTIVE_FUND_CODE, target_shares, actual_amount, latest_signal, ContextInfo)

        log_message("INFO", "激活期交易", f"激活期交易完成，买入{target_shares}股{ACTIVE_FUND_CODE}，金额{actual_amount:,.2f}元", None, ContextInfo)

    except Exception as e:
        error_msg = f"激活期交易失败：{str(e)}"
        log_message("ERROR", "激活期交易", error_msg, None, ContextInfo)


def execute_sleeping_phase_trade(ContextInfo, latest_signal):
    """
    执行沉睡期交易：卖出全部ACTIVE_FUND_CODE，买入等值SLEEPING_FUND_CODE

    Args:
        ContextInfo: iQuant上下文信息对象
        latest_signal: 最新信号信息
    """
    try:
        log_message("INFO", "沉睡期交易", "开始执行沉睡期交易", None, ContextInfo)

        # 查询数据库中ACTIVE_FUND_CODE的持仓记录
        active_holdings = get_holdings_from_db(ACTIVE_FUND_CODE)
        if not active_holdings or active_holdings['shares'] <= 0:
            log_message("INFO", "沉睡期交易", f"数据库中没有{ACTIVE_FUND_CODE}持仓记录，无需卖出", None, ContextInfo)
            return

        # 卖出全部ACTIVE_FUND_CODE
        sell_shares = active_holdings['shares']
        sell_result = place_sell_order(ContextInfo, ACTIVE_FUND_CODE, sell_shares, "沉睡期卖出")

        if not sell_result:
            log_message("ERROR", "沉睡期交易", f"卖出{ACTIVE_FUND_CODE}失败", None, ContextInfo)
            return

        # 计算卖出金额（估算）
        active_price = get_current_price(ContextInfo, ACTIVE_FUND_CODE)
        if active_price is None:
            log_message("ERROR", "沉睡期交易", f"无法获取{ACTIVE_FUND_CODE}当前价格", None, ContextInfo)
            return

        sell_amount = sell_shares * active_price * 0.999  # 扣除手续费估算

        # 买入等值的SLEEPING_FUND_CODE
        sleeping_price = get_current_price(ContextInfo, SLEEPING_FUND_CODE)
        if sleeping_price is None:
            log_message("ERROR", "沉睡期交易", f"无法获取{SLEEPING_FUND_CODE}当前价格", None, ContextInfo)
            return

        buy_shares = math.floor(sell_amount / sleeping_price / 100) * 100
        if buy_shares >= MIN_TRADE_SHARES:
            place_buy_order(ContextInfo, SLEEPING_FUND_CODE, buy_shares, "沉睡期买入")

        # 记录交易到数据库
        record_trade_to_db("SELL", ACTIVE_FUND_CODE, sell_shares, sell_shares * active_price, latest_signal, ContextInfo)
        record_trade_to_db("BUY", SLEEPING_FUND_CODE, buy_shares, buy_shares * sleeping_price, latest_signal, ContextInfo)

        log_message("INFO", "沉睡期交易", f"沉睡期交易完成，卖出{sell_shares}股{ACTIVE_FUND_CODE}，买入{buy_shares}股{SLEEPING_FUND_CODE}", None, ContextInfo)

    except Exception as e:
        error_msg = f"沉睡期交易失败：{str(e)}"
        log_message("ERROR", "沉睡期交易", error_msg, None, ContextInfo)


# ==================== 技术指标计算函数 ====================

def update_technical_indicators(ContextInfo):
    """
    更新技术指标
    获取SIGNAL_FUND_CODE的季线数据，计算EMA指标

    Args:
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 包含当前期和前一期的技术指标数据
    """
    try:
        # 获取日线数据
        required_daily_bars = ContextInfo.barpos + 1

        # 确保g_current_bar_time不为None
        if g_current_bar_time is None:
            current_time = get_current_time(ContextInfo)
        else:
            current_time = g_current_bar_time

        market_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close'],
            stock_code=[SIGNAL_FUND_CODE],
            period='1d',
            end_time=current_time.strftime('%Y%m%d'),
            count=required_daily_bars,
            dividend_type='front',
            fill_data=True
        )

        if market_data is None or len(market_data) == 0:
            log_message("WARNING", "技术指标更新", f"无法获取{SIGNAL_FUND_CODE}的日线数据", None, ContextInfo)
            return None

        # 获取具体股票的数据
        stock_data = market_data.get(SIGNAL_FUND_CODE)
        if stock_data is None or len(stock_data) == 0:
            log_message("WARNING", "技术指标更新", f"无法获取{SIGNAL_FUND_CODE}的日线数据", None, ContextInfo)
            return None

        # 重采样为季线数据
        quarterly_data = resample_daily_to_period(stock_data, EMA_DETECTION_CYCLE)
        if quarterly_data is None or len(quarterly_data) < 2:
            log_message("WARNING", "技术指标更新", f"无法重采样{SIGNAL_FUND_CODE}的季线数据或数据不足", None, ContextInfo)
            return None

        # 获取收盘价数据
        if hasattr(quarterly_data['close'], 'values'):
            close_prices = [float(x) for x in quarterly_data['close'].values]
            stock_dates = quarterly_data.index.tolist()
        else:
            close_prices = [float(x) for x in quarterly_data['close']]
            stock_dates = list(quarterly_data['index'])

        # 计算EMA指标
        ema_values = calculate_ema(close_prices, EMA_PERIOD)

        if len(ema_values) < 2:
            log_message("WARNING", "技术指标更新", "EMA计算结果不足，需要至少2个数据点", None, ContextInfo)
            return None

        # 构建当前期和前一期数据
        current_idx = -1
        previous_idx = -2

        current_data = {
            'close': close_prices[current_idx],
            'high': quarterly_data['high'].iloc[current_idx] if hasattr(quarterly_data['high'], 'iloc') else quarterly_data['high'][current_idx],
            'low': quarterly_data['low'].iloc[current_idx] if hasattr(quarterly_data['low'], 'iloc') else quarterly_data['low'][current_idx],
            'ema_value': ema_values[current_idx],
            'bottom_line': ema_values[current_idx] * BOTTOM_RATIO,
            'top_line': ema_values[current_idx] * TOP_RATIO,
            'stock_date': stock_dates[current_idx]
        }

        previous_data = {
            'close': close_prices[previous_idx],
            'high': quarterly_data['high'].iloc[previous_idx] if hasattr(quarterly_data['high'], 'iloc') else quarterly_data['high'][previous_idx],
            'low': quarterly_data['low'].iloc[previous_idx] if hasattr(quarterly_data['low'], 'iloc') else quarterly_data['low'][previous_idx],
            'ema_value': ema_values[previous_idx],
            'bottom_line': ema_values[previous_idx] * BOTTOM_RATIO,
            'top_line': ema_values[previous_idx] * TOP_RATIO,
            'stock_date': stock_dates[previous_idx]
        }

        return {
            'current': current_data,
            'previous': previous_data
        }

    except Exception as e:
        error_msg = f"技术指标更新失败：{str(e)}"
        log_message("ERROR", "技术指标更新", error_msg, None, ContextInfo)
        return None


def resample_daily_to_period(daily_data, period_type='1q'):
    """
    将日线数据重采样为指定周期的数据

    Args:
        daily_data: 日线数据，包含 open, high, low, close 字段
        period_type: 周期类型，'1q'=季线, '1mon'=月线

    Returns:
        重采样后的数据
    """
    try:
        import pandas as pd
    except ImportError:
        print("[重采样] pandas不可用，跳过重采样")
        return None

    try:
        if daily_data is None:
            return None

        # 将数据转换为DataFrame
        if hasattr(daily_data, 'index'):
            df = daily_data
        else:
            # 如果是字典格式，转换为DataFrame
            df = pd.DataFrame(daily_data)

        if len(df) < 60:  # 至少需要60个交易日的数据
            print(f"[重采样] 数据不足，只有{len(df)}条记录")
            return None

        # 确保索引是日期格式
        if not isinstance(df.index, pd.DatetimeIndex):
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
            else:
                print("[重采样] 无法确定日期列")
                return None

        # 重采样规则
        if period_type == '1q':
            rule = 'Q'  # 季度
        elif period_type == '1mon':
            rule = 'M'  # 月度
        else:
            rule = 'Q'  # 默认季度

        # 执行重采样
        resampled = df.resample(rule).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last'
        }).dropna()

        return resampled

    except Exception as e:
        print(f"[重采样] 重采样失败: {str(e)}")
        return None


def calculate_ema(prices: List[float], period: int) -> List[float]:
    """
    计算指数移动平均线(EMA)

    Args:
        prices: 价格序列
        period: EMA周期

    Returns:
        List[float]: EMA值序列
    """
    if len(prices) < 1:
        return []

    ema_values = []
    multiplier = 2.0 / (period + 1)

    # 第一个EMA值使用第一个价格
    ema_values.append(prices[0])

    # 计算后续的EMA值
    for i in range(1, len(prices)):
        ema = round(ema_values[-1] + multiplier * (prices[i] - ema_values[-1]), 3)
        ema_values.append(ema)

    return ema_values


# ==================== 信号检测和数据库函数 ====================

def check_current_signal(technical_data, ContextInfo):
    """
    检查当前是否有新的买卖信号

    Args:
        technical_data: 技术指标数据
        ContextInfo: iQuant上下文信息对象

    Returns:
        dict: 信号检测结果
    """
    try:
        current_data = technical_data['current']
        previous_data = technical_data['previous']

        current_close = current_data['close']
        current_high = current_data['high']
        current_low = current_data['low']
        bottom_line = current_data['bottom_line']
        top_line = current_data['top_line']

        previous_high = previous_data['high']
        previous_low = previous_data['low']

        # 买入信号：当前期最低价跌破底部线，且收盘价回到底部线上方
        has_buy_signal = (current_low <= bottom_line and current_close > bottom_line and
                         previous_low > bottom_line)

        # 卖出信号：当前期最高价突破顶部线，且收盘价回到顶部线下方
        has_sell_signal = (current_high >= top_line and current_close < top_line and
                          previous_high < top_line)

        if has_buy_signal:
            return {
                'has_signal': True,
                'signal_type': 'ENTERLONG',
                'signal_price': current_close,
                'ema_value': current_data['ema_value'],
                'bottom_line': bottom_line,
                'top_line': None,
                'signal_date': current_data['stock_date']
            }
        elif has_sell_signal:
            return {
                'has_signal': True,
                'signal_type': 'EXITLONG',
                'signal_price': current_close,
                'ema_value': current_data['ema_value'],
                'bottom_line': None,
                'top_line': top_line,
                'signal_date': current_data['stock_date']
            }
        else:
            return {
                'has_signal': False,
                'signal_type': None
            }

    except Exception as e:
        log_message("ERROR", "信号检测", f"检查当前信号失败：{str(e)}", None, ContextInfo)
        return {'has_signal': False, 'signal_type': None}


def get_latest_signal_from_db():
    """
    从数据库获取最新的信号记录

    Returns:
        dict: 最新信号信息
    """
    try:
        if g_db_connection is None:
            return None

        cursor = g_db_connection.cursor()
        cursor.execute("""
            SELECT signal_type, signal_date, signal_price, ema_value, bottom_line, top_line
            FROM signal_history
            WHERE is_valid = 1
            ORDER BY signal_date DESC, id DESC
            LIMIT 1
        """)

        result = cursor.fetchone()
        if result:
            return {
                'signal_type': result[0],
                'signal_date': result[1],
                'signal_price': result[2],
                'ema_value': result[3],
                'bottom_line': result[4],
                'top_line': result[5]
            }
        else:
            return None

    except Exception as e:
        print(f"获取最新信号失败：{str(e)}")
        return None


def record_signal_to_db(signal_info, ContextInfo):
    """
    记录信号到数据库

    Args:
        signal_info: 信号信息
        ContextInfo: iQuant上下文信息对象
    """
    try:
        if g_db_connection is None:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 获取K线位置和日期
        current_kline_position = ContextInfo.barpos
        try:
            current_bar_timestamp = ContextInfo.get_bar_timetag(current_kline_position)
            if current_bar_timestamp:
                kline_datetime = datetime.datetime.fromtimestamp(current_bar_timestamp / 1000)
                current_kline_date = kline_datetime.strftime("%Y%m%d")
            else:
                current_kline_date = datetime.datetime.now().strftime("%Y%m%d")
        except:
            current_kline_date = datetime.datetime.now().strftime("%Y%m%d")

        cursor.execute("""
            INSERT INTO signal_history
            (signal_date, signal_type, signal_price, ema_value, bottom_line, top_line,
             kline_position, kline_date, is_valid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            signal_info['signal_date'],
            signal_info['signal_type'],
            signal_info['signal_price'],
            signal_info['ema_value'],
            signal_info.get('bottom_line'),
            signal_info.get('top_line'),
            current_kline_position,
            current_kline_date,
            1,  # is_valid
            current_time
        ))

        g_db_connection.commit()
        log_message("INFO", "信号记录", f"记录{signal_info['signal_type']}信号到数据库", None, ContextInfo)

    except Exception as e:
        log_message("ERROR", "信号记录", f"记录信号到数据库失败：{str(e)}", None, ContextInfo)


def has_traded_in_current_period(current_phase, latest_signal):
    """
    检查本期间是否已经交易过

    Args:
        current_phase: 当前阶段 ('active' 或 'sleeping')
        latest_signal: 最新信号信息

    Returns:
        bool: 是否已经交易过
    """
    try:
        if g_db_connection is None or latest_signal is None:
            return False

        cursor = g_db_connection.cursor()
        signal_date = latest_signal['signal_date']

        if current_phase == 'active':
            # 检查激活期是否已经买入过
            cursor.execute("""
                SELECT COUNT(*) FROM trade_records
                WHERE trade_type = 'BUY'
                AND stock_code = ?
                AND signal_date = ?
            """, (ACTIVE_FUND_CODE, signal_date))
        else:
            # 检查沉睡期是否已经卖出过
            cursor.execute("""
                SELECT COUNT(*) FROM trade_records
                WHERE trade_type = 'SELL'
                AND stock_code = ?
                AND signal_date = ?
            """, (ACTIVE_FUND_CODE, signal_date))

        result = cursor.fetchone()
        return result[0] > 0 if result else False

    except Exception as e:
        print(f"检查交易记录失败：{str(e)}")
        return False


# ==================== 数据库和日志函数 ====================

def init_database():
    """初始化SQLite数据库"""
    global g_db_connection

    try:
        g_db_connection = sqlite3.connect(DATABASE_PATH)
        cursor = g_db_connection.cursor()

        # 创建信号历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS signal_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                signal_date TEXT NOT NULL,
                signal_type TEXT NOT NULL,
                signal_price REAL NOT NULL,
                ema_value REAL NOT NULL,
                bottom_line REAL,
                top_line REAL,
                kline_position INTEGER,
                kline_date TEXT,
                is_valid INTEGER NOT NULL,
                filter_reason TEXT,
                created_time TEXT NOT NULL
            )
        """)

        # 创建交易记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                trade_date TEXT NOT NULL,
                trade_type TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                shares INTEGER NOT NULL,
                price REAL NOT NULL,
                amount REAL NOT NULL,
                signal_date TEXT,
                signal_type TEXT,
                trade_reason TEXT,
                created_time TEXT NOT NULL
            )
        """)

        # 创建持仓记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS holdings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL UNIQUE,
                shares INTEGER NOT NULL,
                avg_cost REAL NOT NULL,
                total_cost REAL NOT NULL,
                last_update TEXT NOT NULL
            )
        """)

        # 创建交易日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_date TEXT NOT NULL,
                kline_date TEXT,
                log_type TEXT NOT NULL,
                operation TEXT NOT NULL,
                message TEXT NOT NULL,
                details TEXT,
                is_backtest INTEGER DEFAULT 0,
                created_time TEXT NOT NULL
            )
        """)

        # 创建策略状态表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                current_phase TEXT NOT NULL,
                last_check_time TEXT NOT NULL,
                created_time TEXT NOT NULL,
                updated_time TEXT NOT NULL
            )
        """)

        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_signal_history_date ON signal_history(signal_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_records_date ON trade_records(trade_date)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_logs_date ON trade_logs(log_date)")

        g_db_connection.commit()
        print("激进版数据库初始化完成")

    except Exception as e:
        error_msg = f"数据库初始化失败：{str(e)}"
        print(error_msg)
        raise e


def load_strategy_status():
    """加载策略状态"""
    global g_strategy_status

    try:
        if g_db_connection is None:
            raise Exception("数据库连接为空")

        cursor = g_db_connection.cursor()
        cursor.execute("SELECT * FROM strategy_status ORDER BY id DESC LIMIT 1")
        result = cursor.fetchone()

        if result:
            g_strategy_status = {
                'id': result[0],
                'current_phase': result[1],
                'last_check_time': result[2],
                'created_time': result[3],
                'updated_time': result[4]
            }
        else:
            # 创建默认状态
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            g_strategy_status = {
                'id': None,
                'current_phase': 'sleeping',
                'last_check_time': current_time,
                'created_time': current_time,
                'updated_time': current_time
            }

    except Exception as e:
        error_msg = f"加载策略状态失败：{str(e)}"
        print(error_msg)
        raise e


def log_message(log_type: str, operation: str, message: str, details: Optional[Dict] = None, ContextInfo=None):
    """记录日志消息到数据库"""
    try:
        if g_db_connection is None:
            print(f"[{log_type}] {operation}: {message}")
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        details_json = json.dumps(details, ensure_ascii=False) if details else None

        # 获取K线日期和回测模式标识
        kline_date = current_time
        is_backtest = 0

        if ContextInfo:
            try:
                is_backtest = 1 if is_backtest_mode(ContextInfo) else 0
                if is_backtest:
                    kline_time = get_current_time(ContextInfo)
                    kline_date = kline_time.strftime("%Y-%m-%d %H:%M:%S")
            except Exception as e:
                print(f"获取K线时间失败：{str(e)}")

        cursor.execute("""
            INSERT INTO trade_logs (log_date, kline_date, log_type, operation, message, details, is_backtest, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, kline_date, log_type, operation, message, details_json, is_backtest, current_time))

        g_db_connection.commit()

    except Exception as e:
        print(f"记录日志失败：{str(e)}")


# ==================== 交易相关函数 ====================

def get_account_info(ContextInfo):
    """获取账户信息"""
    try:
        if not IQUANT_FUNCTIONS_AVAILABLE:
            # 模拟账户信息
            return {
                'available_cash': 100000.0,
                'margin_available': 50000.0,
                'total_assets': 150000.0
            }

        # 实际获取账户信息的代码
        account_data = get_trade_detail_data(ACCOUNT_ID, "ACCOUNT", "账户")
        if account_data and len(account_data) > 0:
            account = account_data[0]
            return {
                'available_cash': float(account.get('可用金额', 0)),
                'margin_available': float(account.get('融资可用', 0)),
                'total_assets': float(account.get('总资产', 0))
            }
        else:
            return None

    except Exception as e:
        log_message("ERROR", "账户信息", f"获取账户信息失败：{str(e)}", None, ContextInfo)
        return None


def get_current_price(ContextInfo, stock_code):
    """获取股票当前价格"""
    try:
        market_data = ContextInfo.get_market_data_ex(
            fields=['close'],
            stock_code=[stock_code],
            period='1min',
            count=1,
            dividend_type='front',
            fill_data=True
        )

        if market_data and stock_code in market_data:
            stock_data = market_data[stock_code]
            if len(stock_data) > 0:
                return float(stock_data['close'].iloc[-1] if hasattr(stock_data['close'], 'iloc') else stock_data['close'][-1])

        return None

    except Exception as e:
        log_message("ERROR", "价格获取", f"获取{stock_code}价格失败：{str(e)}", None, ContextInfo)
        return None


def place_buy_order(ContextInfo, stock_code, shares, reason):
    """下买单"""
    try:
        log_message("INFO", "下单", f"准备买入{shares}股{stock_code}，原因：{reason}", None, ContextInfo)

        if not IQUANT_FUNCTIONS_AVAILABLE:
            # 模拟下单
            print(f"[模拟] 买入{shares}股{stock_code}")
            return True

        # 实际下单代码
        order_id = passorder(
            opType=23,  # 买入
            orderType=1101,  # 限价单
            accountid=ACCOUNT_ID,
            orderCode=stock_code,
            priceOrder=0,  # 市价
            shareOrder=shares,
            strategyName="激进版策略",
            quickTrade=6
        )

        if order_id:
            log_message("INFO", "下单", f"买入订单提交成功，订单ID：{order_id}", None, ContextInfo)
            return True
        else:
            log_message("ERROR", "下单", f"买入订单提交失败", None, ContextInfo)
            return False

    except Exception as e:
        log_message("ERROR", "下单", f"买入下单失败：{str(e)}", None, ContextInfo)
        return False


def place_sell_order(ContextInfo, stock_code, shares, reason):
    """下卖单"""
    try:
        log_message("INFO", "下单", f"准备卖出{shares}股{stock_code}，原因：{reason}", None, ContextInfo)

        if not IQUANT_FUNCTIONS_AVAILABLE:
            # 模拟下单
            print(f"[模拟] 卖出{shares}股{stock_code}")
            return True

        # 实际下单代码
        order_id = passorder(
            opType=24,  # 卖出
            orderType=1101,  # 限价单
            accountid=ACCOUNT_ID,
            orderCode=stock_code,
            priceOrder=0,  # 市价
            shareOrder=shares,
            strategyName="激进版策略",
            quickTrade=6
        )

        if order_id:
            log_message("INFO", "下单", f"卖出订单提交成功，订单ID：{order_id}", None, ContextInfo)
            return True
        else:
            log_message("ERROR", "下单", f"卖出订单提交失败", None, ContextInfo)
            return False

    except Exception as e:
        log_message("ERROR", "下单", f"卖出下单失败：{str(e)}", None, ContextInfo)
        return False


def record_trade_to_db(trade_type, stock_code, shares, amount, signal_info, ContextInfo):
    """记录交易到数据库"""
    try:
        if g_db_connection is None:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 计算价格
        price = amount / shares if shares > 0 else 0

        cursor.execute("""
            INSERT INTO trade_records
            (trade_date, trade_type, stock_code, shares, price, amount,
             signal_date, signal_type, trade_reason, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            current_time,
            trade_type,
            stock_code,
            shares,
            price,
            amount,
            signal_info.get('signal_date') if signal_info else None,
            signal_info.get('signal_type') if signal_info else None,
            f"激进版{trade_type}",
            current_time
        ))

        # 更新持仓记录
        update_holdings_in_db(stock_code, trade_type, shares, price)

        g_db_connection.commit()
        log_message("INFO", "交易记录", f"记录{trade_type}交易到数据库：{shares}股{stock_code}", None, ContextInfo)

    except Exception as e:
        log_message("ERROR", "交易记录", f"记录交易到数据库失败：{str(e)}", None, ContextInfo)


def update_holdings_in_db(stock_code, trade_type, shares, price):
    """更新数据库中的持仓记录"""
    try:
        if g_db_connection is None:
            return

        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 查询当前持仓
        cursor.execute("SELECT shares, avg_cost, total_cost FROM holdings WHERE stock_code = ?", (stock_code,))
        result = cursor.fetchone()

        if result:
            current_shares, current_avg_cost, current_total_cost = result
        else:
            current_shares, current_avg_cost, current_total_cost = 0, 0, 0

        if trade_type == "BUY":
            # 买入：增加持仓
            new_shares = current_shares + shares
            new_total_cost = current_total_cost + (shares * price)
            new_avg_cost = new_total_cost / new_shares if new_shares > 0 else 0
        else:
            # 卖出：减少持仓
            new_shares = current_shares - shares
            if new_shares <= 0:
                new_shares = 0
                new_avg_cost = 0
                new_total_cost = 0
            else:
                new_avg_cost = current_avg_cost  # 卖出不改变平均成本
                new_total_cost = new_shares * new_avg_cost

        # 更新或插入持仓记录
        if result:
            cursor.execute("""
                UPDATE holdings
                SET shares = ?, avg_cost = ?, total_cost = ?, last_update = ?
                WHERE stock_code = ?
            """, (new_shares, new_avg_cost, new_total_cost, current_time, stock_code))
        else:
            cursor.execute("""
                INSERT INTO holdings (stock_code, shares, avg_cost, total_cost, last_update)
                VALUES (?, ?, ?, ?, ?)
            """, (stock_code, new_shares, new_avg_cost, new_total_cost, current_time))

    except Exception as e:
        print(f"更新持仓记录失败：{str(e)}")


def get_holdings_from_db(stock_code):
    """从数据库获取持仓信息"""
    try:
        if g_db_connection is None:
            return None

        cursor = g_db_connection.cursor()
        cursor.execute("SELECT shares, avg_cost, total_cost FROM holdings WHERE stock_code = ?", (stock_code,))
        result = cursor.fetchone()

        if result:
            return {
                'shares': result[0],
                'avg_cost': result[1],
                'total_cost': result[2]
            }
        else:
            return {'shares': 0, 'avg_cost': 0, 'total_cost': 0}

    except Exception as e:
        print(f"获取持仓信息失败：{str(e)}")
        return None


# ==================== 辅助函数 ====================

def down_history_data(stock_code, period, start_date, end_date):
    """
    下载历史数据（模拟函数）

    Args:
        stock_code: 股票代码
        period: 周期
        start_date: 开始日期
        end_date: 结束日期
    """
    try:
        # 在实际环境中，这里会调用iQuant的数据下载函数
        # 这里只是一个模拟实现
        print(f"[模拟] 下载{stock_code}的{period}数据，从{start_date}到{end_date}")
        return True
    except Exception as e:
        print(f"下载历史数据失败：{str(e)}")
        return False


def get_strategy_performance_summary():
    """
    获取策略表现摘要

    Returns:
        dict: 策略表现统计
    """
    try:
        if g_db_connection is None:
            return {
                'total_trades': 0,
                'successful_trades': 0,
                'failed_trades': 0,
                'total_signals': 0,
                'valid_signals': 0,
                'filtered_signals': 0,
                'current_phase': 'unknown'
            }

        cursor = g_db_connection.cursor()

        # 统计交易次数
        cursor.execute("SELECT COUNT(*) FROM trade_records")
        total_trades = cursor.fetchone()[0] or 0

        # 统计信号次数
        cursor.execute("SELECT COUNT(*) FROM signal_history")
        total_signals = cursor.fetchone()[0] or 0

        cursor.execute("SELECT COUNT(*) FROM signal_history WHERE is_valid = 1")
        valid_signals = cursor.fetchone()[0] or 0

        # 获取当前阶段
        current_phase = g_strategy_status.get('current_phase', 'unknown') if g_strategy_status else 'unknown'

        return {
            'total_trades': total_trades,
            'successful_trades': total_trades,  # 简化统计
            'failed_trades': 0,
            'total_signals': total_signals,
            'valid_signals': valid_signals,
            'filtered_signals': total_signals - valid_signals,
            'current_phase': current_phase
        }

    except Exception as e:
        print(f"获取策略表现摘要失败：{str(e)}")
        return {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_signals': 0,
            'valid_signals': 0,
            'filtered_signals': 0,
            'current_phase': 'unknown'
        }


def validate_strategy_state():
    """
    验证策略状态是否正常

    Returns:
        bool: 状态是否正常
    """
    try:
        if g_strategy_status is None:
            return False

        if g_db_connection is None:
            return False

        # 检查必要的字段
        required_fields = ['current_phase', 'last_check_time']
        for field in required_fields:
            if field not in g_strategy_status:
                return False

        return True

    except Exception as e:
        print(f"验证策略状态失败：{str(e)}")
        return False


# ==================== 程序入口点 ====================

if __name__ == "__main__":
    print("激进版择时量化投资策略")
    print("这是一个用于iQuant平台的策略文件")
    print("请在iQuant平台中加载此策略文件")
