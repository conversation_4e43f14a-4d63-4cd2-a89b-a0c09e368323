#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下单错误回调函数测试脚本
验证 orderError_callback 函数是否正确处理下单错误并更新状态
"""

import sqlite3
import datetime
import uuid

DATABASE_PATH = "gytrading2.db"

def create_test_data():
    """创建测试数据"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 创建测试UUID
        test_uuid = f"test-error-{str(uuid.uuid4())}"
        
        print(f"【测试】创建测试数据，UUID: {test_uuid}")
        
        # 1. 创建 trade_orders 记录
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_ERROR", 1000,
              "PENDING", current_time))
        
        trade_order_id = cursor.lastrowid
        print(f"【测试】创建 trade_orders 记录，ID: {trade_order_id}")
        
        # 2. 创建 trade_task_queue 记录
        cursor.execute("""
            INSERT INTO trade_task_queue
            (task_group_id, task_type, stock_code, target_shares, target_amount,
             estimated_price, estimated_fees, task_status, order_uuid, order_id, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (str(uuid.uuid4()), "BUY_159915_CASH", "159915.SZ", 1000, 2500.0,
              2.5, 5.0, "WAITING_CALLBACK", test_uuid, "TEST12345", current_time))
        
        task_id = cursor.lastrowid
        print(f"【测试】创建 trade_task_queue 记录，ID: {task_id}")
        
        conn.commit()
        conn.close()
        
        return test_uuid, trade_order_id, task_id
        
    except Exception as e:
        print(f"【测试】创建测试数据失败：{str(e)}")
        return None, None, None

def simulate_order_error_callback(test_uuid):
    """模拟下单错误回调"""
    try:
        print(f"【测试】模拟下单错误回调，UUID: {test_uuid}")
        
        # 模拟 orderArgs 对象
        class MockOrderArgs:
            def __init__(self, strategy_name):
                self.strategyName = strategy_name
                self.stockCode = "159915.SZ"
                self.orderType = 23  # 买入
                self.orderAmount = 1000
                self.orderPrice = 2.5
        
        # 测试两种格式的 strategyName
        test_cases = [
            f"价值平均策略_&&&_{test_uuid}",  # 包含策略名的格式
            test_uuid  # 直接是UUID的格式
        ]
        
        for i, strategy_name in enumerate(test_cases):
            print(f"\n【测试】测试用例 {i+1}: strategyName = {strategy_name}")
            
            # 重新创建测试数据（因为第一次会被更新）
            if i > 0:
                test_uuid_new, _, _ = create_test_data()
                if i == 1:
                    strategy_name = test_uuid_new
            
            mock_order_args = MockOrderArgs(strategy_name)
            mock_error_msg = f"测试错误信息_{i+1}：资金不足"
            
            # 导入并调用 orderError_callback 函数
            import sys
            sys.path.append('.')
            from value_averaging_strategy import orderError_callback
            
            # 模拟 ContextInfo（可以为 None）
            mock_context_info = None
            
            # 调用回调函数
            orderError_callback(mock_context_info, mock_order_args, mock_error_msg)
            
            print(f"【测试】回调函数调用完成")
        
    except Exception as e:
        print(f"【测试】模拟回调失败：{str(e)}")
        import traceback
        traceback.print_exc()

def verify_results(test_uuid):
    """验证结果"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        print(f"\n【验证】检查状态更新结果，UUID: {test_uuid}")
        
        # 1. 检查 trade_orders 表（通过股票代码和股数匹配）
        cursor.execute("""
            SELECT order_status, error_message
            FROM trade_orders
            WHERE stock_code = '159915.SZ'
            AND target_shares = 1000
            AND order_reason = 'TEST_ERROR'
            ORDER BY id DESC LIMIT 1
        """, ())
        
        trade_order_result = cursor.fetchone()
        if trade_order_result:
            status, error_msg = trade_order_result
            print(f"【验证】trade_orders 状态: {status}")
            print(f"【验证】trade_orders 错误信息: {error_msg}")
            
            if status == "FAILED":
                print("✅ trade_orders 状态更新正确")
            else:
                print("❌ trade_orders 状态更新失败")
        else:
            print("❌ 未找到 trade_orders 记录")
        
        # 2. 检查 trade_task_queue 表
        cursor.execute("""
            SELECT task_status, error_message, updated_time
            FROM trade_task_queue 
            WHERE order_uuid = ?
        """, (test_uuid,))
        
        task_queue_result = cursor.fetchone()
        if task_queue_result:
            status, error_msg, updated_time = task_queue_result
            print(f"【验证】trade_task_queue 状态: {status}")
            print(f"【验证】trade_task_queue 错误信息: {error_msg}")
            print(f"【验证】trade_task_queue 更新时间: {updated_time}")
            
            if status == "FAILED":
                print("✅ trade_task_queue 状态更新正确")
            else:
                print("❌ trade_task_queue 状态更新失败")
        else:
            print("❌ 未找到 trade_task_queue 记录")
        
        # 3. 检查 trade_task_log 表
        cursor.execute("""
            SELECT log_level, log_category, log_message, log_time
            FROM trade_task_log 
            WHERE log_message LIKE '%下单失败%'
            ORDER BY id DESC LIMIT 1
        """)
        
        log_result = cursor.fetchone()
        if log_result:
            level, category, message, log_time = log_result
            print(f"【验证】trade_task_log 日志级别: {level}")
            print(f"【验证】trade_task_log 日志分类: {category}")
            print(f"【验证】trade_task_log 日志信息: {message}")
            print(f"【验证】trade_task_log 记录时间: {log_time}")
            
            if level == "ERROR" and category == "ORDER_ERROR":
                print("✅ trade_task_log 错误日志记录正确")
            else:
                print("❌ trade_task_log 错误日志记录失败")
        else:
            print("❌ 未找到 trade_task_log 错误日志")
        
        conn.close()
        
    except Exception as e:
        print(f"【验证】验证结果失败：{str(e)}")

def cleanup_test_data(test_uuid):
    """清理测试数据"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        print(f"\n【清理】清理测试数据，UUID: {test_uuid}")
        
        # 删除测试数据
        cursor.execute("DELETE FROM trade_orders WHERE order_uuid LIKE 'test-error-%'")
        cursor.execute("DELETE FROM trade_task_queue WHERE order_uuid LIKE 'test-error-%'")
        cursor.execute("DELETE FROM trade_task_log WHERE log_message LIKE '%测试错误信息%'")
        
        conn.commit()
        conn.close()
        
        print("【清理】测试数据清理完成")
        
    except Exception as e:
        print(f"【清理】清理测试数据失败：{str(e)}")

def main():
    print("=" * 60)
    print("下单错误回调函数测试")
    print("=" * 60)
    
    # 1. 创建测试数据
    test_uuid, trade_order_id, task_id = create_test_data()
    if not test_uuid:
        print("❌ 创建测试数据失败，退出测试")
        return
    
    try:
        # 2. 模拟下单错误回调
        simulate_order_error_callback(test_uuid)
        
        # 3. 验证结果
        verify_results(test_uuid)
        
    finally:
        # 4. 清理测试数据
        cleanup_test_data(test_uuid)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
