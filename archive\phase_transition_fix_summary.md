# 阶段切换修复总结

## 问题描述

用户反馈在从sleeping到active阶段切换时，现有代码存在以下问题：

1. **错误的买入逻辑**：使用账户所有可用资金全仓买入，而不是按价值平均策略计算
2. **价格获取问题**：回测模式下应该使用当前K线收盘价，而不是最新价格
3. **交易队列支持**：需要确保回测模式也能使用交易队列机制

## 用户需求明确

### 1. 价值平均策略买入逻辑
- **不是**：用账户所有可用资金全仓买入
- **而是**：按照价值平均策略计算应买入的股数
  - 从5年内收盘价最高位作为第1期开始计算
  - 计算到当前K线日期是第几期
  - 目标金额 = 期数 × 每期投入金额
  - 应买股数 = 目标金额 ÷ 当前价格

### 2. 回测模式交易处理
- 买入和卖出都使用交易队列方式
- 价格区别：
  - **回测模式**：使用当前K线的收盘价
  - **实盘模式**：使用当前最新价格

### 3. 期数计算
- 基于投资周期INVESTMENT_CYCLE进行计算
- 不考虑当前持仓，重新开始计算

## 修复实现

### 1. 修改期数计算函数

```python
def calculate_current_period(start_date: str, ContextInfo=None) -> int:
    """
    计算当前期数，支持回测模式和多种投资周期格式
    """
    # 获取当前时间（回测适配）
    if ContextInfo and is_backtest_mode(ContextInfo):
        current_dt = get_current_time(ContextInfo)
    else:
        current_dt = datetime.datetime.now()

    # 支持"1mon"、"1q"等格式
    if INVESTMENT_CYCLE in ["月线", "1mon"]:
        months_diff = (current_dt.year - start_dt.year) * 12 + (current_dt.month - start_dt.month)
        return max(1, months_diff + 1)
```

### 2. 创建回测适配的价格获取函数

```python
def get_current_price(stock_code: str, ContextInfo) -> float:
    """获取股票当前价格（回测适配）"""
    if is_backtest_mode(ContextInfo):
        return get_backtest_current_price(stock_code, ContextInfo)
    else:
        return get_realtime_current_price(stock_code, ContextInfo)

def get_backtest_current_price(stock_code: str, ContextInfo) -> float:
    """获取回测模式下的当前价格（当前K线收盘价）"""
    # 获取当前K线的收盘价
    market_data = ContextInfo.get_market_data_ex(
        fields=['close'],
        stock_code=[stock_code],
        period='1d',
        count=1,
        end_time=g_current_bar_time.strftime('%Y%m%d'),
        dividend_type='front',
        fill_data=True
    )
```

### 3. 修改阶段切换逻辑

完全重写了从sleeping到active的切换逻辑：

```python
# 3. 按价值平均策略买入159915
log_message("INFO", "阶段切换", "步骤3：按价值平均策略买入159915")

# 计算当前期数
current_period = calculate_current_period(g_strategy_status['start_period_date'], ContextInfo)

# 计算目标金额
target_amount = current_period * PERIOD_INVESTMENT_AMOUNT

# 获取当前价格（回测模式下使用当前K线收盘价）
current_price = get_current_price(ACTIVE_FUND_CODE, ContextInfo)

# 计算应买入股数（不考虑当前持仓，重新开始计算）
shares_to_buy = int(target_amount / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
```

### 4. 交易队列系统确认

验证了现有的`execute_trade_order`函数已经支持回测模式：

```python
def execute_trade_order(stock_code: str, order_type: str, shares: int, order_reason: str, ContextInfo) -> bool:
    # 检查是否为回测模式
    if is_backtest_mode(ContextInfo):
        # 回测模式：只做模拟记录，不真实下单
        current_price = get_current_price(stock_code, ContextInfo)
        # 模拟更新持仓记录
        simulate_position_update(stock_code, order_type, shares, current_price, ContextInfo)
        return True
```

## 测试验证

### 测试场景
- 起始期：2023-01-01（第1期）
- 当前时间：2023-04-15（第4期）
- 每期投入：10000元
- 投资周期：1mon（月线）
- 当前价格：2.5元

### 测试结果
```
✅ 当前期数计算：4
✅ 目标金额计算：40000.00元（第4期 × 10000元/期）
✅ 当前价格获取：2.5000元
✅ 应买入股数计算：16000股
✅ 资金检查：可用资金50000元，需要资金40000元，资金充足
✅ 交易执行：成功
```

### 验证要点
1. ✅ **期数计算正确**：从2023-01-01到2023-04-15，正确计算为第4期
2. ✅ **目标金额正确**：4期 × 10000元 = 40000元
3. ✅ **股数计算正确**：40000元 ÷ 2.5元 = 16000股
4. ✅ **价格获取正确**：回测模式使用当前K线收盘价
5. ✅ **交易执行正确**：使用交易队列系统进行模拟交易

## 关键改进

### 1. 策略逻辑修正
- **之前**：全仓买入（错误）
- **现在**：按价值平均策略精确计算买入股数

### 2. 回测适配
- **之前**：可能使用实时价格
- **现在**：回测模式使用当前K线收盘价，实盘模式使用最新价格

### 3. 期数计算增强
- **之前**：可能不支持"1mon"格式
- **现在**：支持多种投资周期格式，并适配回测模式时间

### 4. 资金管理优化
- 增加了资金充足性检查
- 支持部分买入（当资金不足时买入可负担的最大股数）
- 详细的日志记录便于调试

## 使用建议

### 1. 参数配置
确保以下参数正确设置：
- `PERIOD_INVESTMENT_AMOUNT`：每期投入金额
- `INVESTMENT_CYCLE`：投资周期（支持"1mon"、"1q"等）
- `MIN_TRADE_SHARES`：最小交易股数

### 2. 监控日志
关注以下关键日志：
- 期数计算结果
- 目标金额计算
- 当前价格获取（区分回测/实盘模式）
- 资金充足性检查
- 交易执行结果

### 3. 回测验证
- 在回测模式下验证策略逻辑
- 确认价格获取使用的是当前K线收盘价
- 验证期数计算的准确性

## 总结

修复后的阶段切换逻辑完全符合用户需求：
1. ✅ 使用价值平均策略而非全仓买入
2. ✅ 回测模式使用当前K线收盘价
3. ✅ 支持交易队列机制
4. ✅ 正确的期数计算和目标金额计算

策略现在能够在回测和实盘模式下正确执行价值平均投资策略。
