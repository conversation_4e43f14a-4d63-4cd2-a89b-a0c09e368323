# 交易任务队列系统使用说明

## 系统概述

交易任务队列系统是为了解决异步交易操作而设计的，它将复杂的买入流程分解为多个独立的任务，通过队列机制确保每个任务按正确顺序执行，并通过回调机制处理异步交易结果。

## 核心特性

### 1. 异步任务处理
- **非阻塞执行**：不会阻塞策略主流程
- **任务依赖管理**：确保任务按正确顺序执行
- **回调驱动**：通过iQuant回调机制处理交易结果

### 2. 完整的交易流程
- **智能资金调配**：510720卖出 → 现金买入 → 融资买入
- **费用计算**：准确计算手续费和印花税
- **账户快照**：记录关键时点的账户状态

### 3. 可靠性保障
- **超时处理**：分级超时检测和处理
- **状态查询**：主动查询订单状态
- **完整日志**：记录所有操作和状态变化

## 系统架构

### 核心组件

1. **TradeTaskQueue**：任务队列管理器
2. **TradeTaskExecutor**：任务执行器
3. **TradeTaskCallbackHandler**：回调处理器

### 数据库表结构

1. **trade_task_queue**：任务队列主表
2. **trade_task_log**：任务日志表（合并了原执行记录表功能）
3. **account_snapshot**：账户快照表

## 使用方法

### 1. 基本用法

```python
# 原有的同步调用
success = execute_buy_order(stock_code, shares, reason, ContextInfo, account_info)

# 现在的异步调用（内部已自动切换）
success = execute_buy_order(stock_code, shares, reason, ContextInfo, account_info)
# 返回True表示任务组创建成功，实际交易将异步执行
```

### 2. 任务流程

#### 买入159915的完整流程：
1. **创建任务组**：生成唯一的任务组ID
2. **创建卖出任务**：卖出510720获取资金
3. **创建现金买入任务**：用可用现金买入159915
4. **创建融资买入任务**：用融资额度买入剩余份额

#### 任务执行顺序：
```
卖出510720 → 现金买入159915 → 融资买入159915（如需要）
```

### 3. 回调集成

在iQuant的回调函数中添加任务队列处理：

```python
def order_callback(ContextInfo, orderInfo):
    # 原有的回调处理...
    
    # 添加任务队列回调处理
    task_queue_order_callback(ContextInfo, orderInfo)

def deal_callback(ContextInfo, dealInfo):
    # 原有的回调处理...
    
    # 添加任务队列回调处理
    task_queue_deal_callback(ContextInfo, dealInfo)
```

### 4. 主循环集成

在策略主循环中添加任务队列处理：

```python
def handlebar(ContextInfo):
    # 原有的策略逻辑...
    
    # 任务队列处理（已自动集成）
    # task_queue_process_pending_tasks(ContextInfo)
```

## 配置参数

### 费用配置
```python
COMMISSION_FEE_RATE = 0.0003        # 手续费率
COMMISSION_FEE_MIN = 5.0            # 最低手续费
SELL_TAX_RATE = 0.001               # 印花税率（卖出）
MARGIN_INTEREST_RATE = 0.0          # 融资利率
```

### 超时配置
```python
timeout_levels = {
    'WARNING': 60,      # 1分钟：记录警告
    'QUERY': 300,       # 5分钟：主动查询状态
    'ALERT': 1800,      # 30分钟：发送告警
}
```

## 监控和调试

### 1. 日志监控

关键日志类别：
- **TASK_CREATE**：任务创建
- **TASK_EXECUTE**：任务执行
- **CALLBACK**：回调处理
- **TIMEOUT**：超时处理
- **SNAPSHOT**：账户快照

### 2. 数据库查询

#### 查看任务组状态：
```sql
SELECT task_group_id, task_type, task_status, target_shares, order_id
FROM trade_task_queue 
WHERE task_group_id = 'your-task-group-id'
ORDER BY created_time;
```

#### 查看执行记录：
```sql
SELECT t.task_type, l.execution_step, l.actual_shares, l.actual_price
FROM trade_task_queue t
JOIN trade_task_log l ON t.id = l.task_id
WHERE t.task_group_id = 'your-task-group-id'
  AND l.execution_step IS NOT NULL
ORDER BY l.log_time;
```

#### 查看任务日志：
```sql
SELECT log_time, log_level, log_category, log_message
FROM trade_task_log
WHERE task_group_id = 'your-task-group-id'
ORDER BY log_time;
```

### 3. 状态监控

#### 任务状态说明：
- **PENDING**：待执行
- **EXECUTING**：执行中
- **WAITING_CALLBACK**：等待回调
- **COMPLETED**：已完成
- **FAILED**：失败
- **TIMEOUT**：超时

#### 监控要点：
1. 检查是否有长时间处于WAITING_CALLBACK状态的任务
2. 关注FAILED状态的任务及其错误原因
3. 监控超时告警和状态查询结果

## 故障处理

### 1. 常见问题

#### 任务长时间等待回调
- **原因**：网络延迟、市场因素、系统问题
- **处理**：系统会自动进行状态查询和告警
- **手动处理**：查看订单状态，必要时人工介入

#### 任务执行失败
- **原因**：资金不足、股票停牌、系统错误
- **处理**：查看error_message字段了解具体原因
- **恢复**：根据错误原因调整参数后重新创建任务

### 2. 手动干预

#### 查询订单状态：
```python
order_obj = get_value_by_order_id(order_id, account_id, 'stock', 'order')
status = order_obj.m_nOrderStatus
```

#### 手动完成任务：
```sql
UPDATE trade_task_queue 
SET task_status = 'COMPLETED', completed_time = datetime('now')
WHERE id = task_id;
```

## 性能优化

### 1. 数据库优化
- 定期清理历史任务数据
- 监控数据库大小和查询性能
- 适当调整索引策略

### 2. 任务优化
- 合理设置超时时间
- 优化任务创建逻辑
- 减少不必要的日志记录

## 扩展功能

### 1. 告警通知
可以扩展告警机制，支持：
- 邮件通知
- 微信通知
- 短信通知

### 2. 任务重试
可以添加任务重试机制：
- 自动重试失败任务
- 可配置重试次数和间隔
- 智能重试策略

### 3. 性能统计
可以添加性能统计功能：
- 任务执行时间统计
- 成功率统计
- 费用统计分析

## 注意事项

1. **数据一致性**：确保数据库操作的原子性
2. **异常处理**：所有操作都有完善的异常处理
3. **日志记录**：重要操作都有详细的日志记录
4. **状态同步**：确保任务状态与实际交易状态同步
5. **资源管理**：合理管理数据库连接和内存使用

这个交易任务队列系统为价值平均策略提供了可靠的异步交易能力，确保复杂的交易流程能够正确、完整地执行。
