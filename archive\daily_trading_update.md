# 每日交易控制更新总结

## 修改目标

将策略从"每天最多交易一次"改为"每天一次买入一次卖出"，并且仅使用全局变量判断交易状态，不依赖数据库，方便多次重启策略测试。

## 主要修改内容

### 1. 全局变量重新设计

#### 修改前
```python
g_last_trade_date = None          # 最后交易日期（YYYY-MM-DD格式）
g_trade_count = 0                 # 交易计数器
g_today_traded = False            # 今天是否已经交易过
```

#### 修改后
```python
g_current_date = None             # 当前日期（YYYY-MM-DD格式）
g_trade_count = 0                 # 交易计数器
g_today_buy_done = False          # 今天是否已经买入
g_today_sell_done = False         # 今天是否已经卖出
```

### 2. 状态管理函数重构

#### 新增函数
```python
def init_strategy_status():
    """初始化策略状态（仅使用全局变量）"""
    # 检查是否是新的一天，如果是则重置交易状态
    # 从数据库加载历史交易计数（仅用于显示）

def check_new_day():
    """检查是否是新的一天，如果是则重置交易状态"""
    # 自动检测日期变化并重置交易状态

def can_buy_today() -> bool:
    """检查今天是否可以买入"""

def can_sell_today() -> bool:
    """检查今天是否可以卖出"""

def mark_buy_done():
    """标记今天买入已完成"""

def mark_sell_done():
    """标记今天卖出已完成"""

def get_next_trade_type() -> str:
    """获取下一个可执行的交易类型"""
    # 返回 "BUY", "SELL", 或 None
```

### 3. 交易逻辑优化

#### 交易类型判断
```python
# 在 handlebar 中
next_trade_type = get_next_trade_type()
if not next_trade_type:
    print("今天买卖都已完成，跳过交易")
    return

# 执行对应类型的交易
execute_simple_trading_logic(ContextInfo, next_trade_type)
```

#### 交易执行逻辑
```python
def execute_simple_trading_logic(ContextInfo, trade_type: str):
    # 根据传入的交易类型执行相应操作
    if trade_type == "BUY":
        task_type = TaskType.BUY_TEST.value
        reason = "今日买入交易 - 测试买入"
        # 执行后调用 mark_buy_done()
    elif trade_type == "SELL":
        task_type = TaskType.SELL_TEST.value
        reason = "今日卖出交易 - 测试卖出"
        # 执行后调用 mark_sell_done()
```

### 4. 状态持久化策略

#### 设计原则
- **全局变量优先**：交易状态完全由内存中的全局变量控制
- **数据库仅统计**：数据库只用于记录历史交易和显示统计信息
- **自动日期检测**：每次调用时自动检查是否是新的一天
- **重启友好**：策略重启后会重新开始，适合测试

#### 日期变化处理
```python
def check_new_day():
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    
    if g_current_date != current_date:
        print(f"检测到新的一天：{g_current_date} → {current_date}")
        g_current_date = current_date
        g_today_buy_done = False
        g_today_sell_done = False
        print("✓ 已重置今天的交易状态")
        return True
    
    return False
```

## 工作流程

### 1. 策略启动
1. 调用 `init_strategy_status()` 初始化状态
2. 设置当前日期，重置交易状态
3. 从数据库加载历史统计（仅用于显示）

### 2. 每次 handlebar 调用
1. 处理任务队列中的待执行任务
2. 调用 `get_next_trade_type()` 获取下一个可执行的交易类型
3. 如果有可执行的交易类型，执行相应交易
4. 标记对应的交易状态为完成

### 3. 交易状态管理
- **买入完成**：`g_today_buy_done = True`
- **卖出完成**：`g_today_sell_done = True`
- **日期变化**：自动重置所有交易状态

### 4. 交易顺序
- 优先执行买入（如果今天还未买入）
- 然后执行卖出（如果今天还未卖出）
- 都完成后跳过后续交易

## 优势特点

### 1. 测试友好
- ✅ 每次重启策略都会重新开始
- ✅ 不依赖数据库状态，避免状态混乱
- ✅ 方便多次测试和调试

### 2. 逻辑清晰
- ✅ 每天最多一次买入、一次卖出
- ✅ 交易状态一目了然
- ✅ 自动处理日期变化

### 3. 状态可控
- ✅ 全局变量控制，状态透明
- ✅ 实时显示当前交易状态
- ✅ 支持手动重置（重启策略）

## 预期运行效果

### 第一次运行（买入）
```
策略状态初始化完成：
  当前日期：2025-08-14
  今天买入状态：未完成
  今天卖出状态：未完成
  下次可执行：BUY（买入）

准备执行买入操作：159915.SZ 100股
✓ 交易任务已创建：任务组ID=xxx，任务ID=1，订单UUID=abc-123
✓ 已标记今天(2025-08-14)买入完成
```

### 第二次运行（卖出）
```
当前策略状态：
  今天买入状态：已完成
  今天卖出状态：未完成
  下次可执行：SELL（卖出）

准备执行卖出操作：159915.SZ 100股
✓ 交易任务已创建：任务组ID=yyy，任务ID=2，订单UUID=def-456
✓ 已标记今天(2025-08-14)卖出完成
```

### 第三次运行（完成）
```
今天(2025-08-14)买卖都已完成，跳过交易
```

### 第二天运行（重置）
```
检测到新的一天：2025-08-14 → 2025-08-15
✓ 已重置今天的交易状态
下次可执行：BUY（买入）
```

## 测试建议

1. **单日测试**：
   - 启动策略，观察买入执行
   - 重启策略，观察卖出执行
   - 再次重启，观察跳过交易

2. **跨日测试**：
   - 修改系统时间测试日期变化
   - 观察交易状态自动重置

3. **状态验证**：
   - 检查全局变量状态变化
   - 验证数据库记录正确性

这个修改使策略更适合测试环境，每次重启都能重新开始，同时支持每天一次买入一次卖出的完整交易流程。
