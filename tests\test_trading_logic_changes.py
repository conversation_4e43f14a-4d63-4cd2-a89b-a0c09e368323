# -*- coding: utf-8 -*-
"""
测试交易逻辑修改
验证激活期内卖出和阶段转换的新逻辑
"""

import sys
import os
import datetime
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_active_period_sell_logic():
    """测试激活期内卖出逻辑：应该只卖出159915，不买入510720"""
    print("=" * 60)
    print("测试1: 激活期内卖出逻辑")
    print("=" * 60)
    
    try:
        # 导入需要的模块
        from value_averaging_strategy import _execute_sell_159915_logic, g_trade_task_queue
        from value_averaging_strategy import ACTIVE_FUND_CODE, MIN_TRADE_SHARES
        
        # 模拟全局变量
        import value_averaging_strategy
        value_averaging_strategy.g_trade_task_queue = Mock()
        value_averaging_strategy.g_trade_task_queue.create_task_group = Mock(return_value="test_group_id")
        value_averaging_strategy.g_trade_task_queue.create_task = Mock(return_value=("task_id", "order_uuid"))
        value_averaging_strategy.g_trade_task_queue.log_task_message = Mock()
        value_averaging_strategy.g_trade_task_queue.calculate_fees = Mock(return_value=5.0)
        
        # 模拟持仓和价格查询函数
        with patch('value_averaging_strategy.get_current_position') as mock_position, \
             patch('value_averaging_strategy.get_current_price') as mock_price:
            
            # 设置模拟返回值
            mock_position.return_value = {'shares': 1000, 'avg_cost': 2.5, 'market_value': 2600}
            mock_price.return_value = 2.6  # 159915当前价格
            
            # 模拟ContextInfo
            mock_context = Mock()
            
            # 执行测试
            task_group_id = _execute_sell_159915_logic(
                task_group_id="test_group",
                target_shares=500,
                order_reason="VALUE_AVERAGE",
                ContextInfo=mock_context
            )
            
            print(f"✅ 任务组ID: {task_group_id}")
            
            # 验证调用
            create_task_calls = value_averaging_strategy.g_trade_task_queue.create_task.call_args_list
            print(f"✅ 创建任务调用次数: {len(create_task_calls)}")
            
            # 检查是否只创建了卖出任务，没有创建买入510720任务
            sell_task_created = False
            buy_510720_task_created = False
            
            for call in create_task_calls:
                args, kwargs = call
                task_type = kwargs.get('task_type', '')
                stock_code = kwargs.get('stock_code', '')
                
                if 'SELL_159915' in task_type:
                    sell_task_created = True
                    print(f"✅ 发现卖出159915任务: {task_type}, 股票代码: {stock_code}")
                elif 'BUY_510720' in task_type:
                    buy_510720_task_created = True
                    print(f"❌ 意外发现买入510720任务: {task_type}, 股票代码: {stock_code}")
            
            if sell_task_created and not buy_510720_task_created:
                print("✅ 测试通过：激活期内只卖出159915，不买入510720")
            else:
                print("❌ 测试失败：逻辑不符合预期")
                
            # 检查日志消息
            log_calls = value_averaging_strategy.g_trade_task_queue.log_task_message.call_args_list
            for call in log_calls:
                args, kwargs = call
                message = kwargs.get('message', '')
                if '现金将保留在账户中' in message:
                    print(f"✅ 发现预期日志: {message}")
                    
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()


def test_phase_transition_logic():
    """测试阶段转换逻辑：应该按份额转换"""
    print("\n" + "=" * 60)
    print("测试2: 激活期到沉睡期转换逻辑")
    print("=" * 60)
    
    try:
        from value_averaging_strategy import execute_active_to_sleeping_transition_async
        import value_averaging_strategy
        
        # 重置模拟对象
        value_averaging_strategy.g_trade_task_queue = Mock()
        value_averaging_strategy.g_trade_task_queue.create_task_group = Mock(return_value="transition_group_id")
        value_averaging_strategy.g_trade_task_queue.create_task = Mock(return_value=("task_id", "order_uuid"))
        value_averaging_strategy.g_trade_task_queue.log_task_message = Mock()
        value_averaging_strategy.g_trade_task_queue.calculate_fees = Mock(return_value=10.0)
        value_averaging_strategy.g_trade_task_queue.create_account_snapshot = Mock()
        
        # 模拟持仓、价格和账户信息
        with patch('value_averaging_strategy.get_current_position') as mock_position, \
             patch('value_averaging_strategy.get_current_price') as mock_price, \
             patch('value_averaging_strategy.get_account_info') as mock_account:
            
            # 设置模拟返回值
            mock_position.return_value = {'shares': 2000, 'avg_cost': 2.5, 'market_value': 5200}
            
            def price_side_effect(stock_code, context):
                if '159915' in stock_code:
                    return 2.6  # 159915价格
                elif '510720' in stock_code:
                    return 1.3  # 510720价格
                return 1.0
            
            mock_price.side_effect = price_side_effect
            mock_account.return_value = {'available_cash': 10000}  # 充足现金
            
            # 模拟信号详情和ContextInfo
            signal_details = {'signal_type': 'EXITLONG'}
            mock_context = Mock()
            
            # 执行测试
            task_group_id = execute_active_to_sleeping_transition_async(
                ContextInfo=mock_context,
                signal_details=signal_details
            )
            
            print(f"✅ 转换任务组ID: {task_group_id}")
            
            # 验证计算逻辑
            log_calls = value_averaging_strategy.g_trade_task_queue.log_task_message.call_args_list
            for call in log_calls:
                args, kwargs = call
                message = kwargs.get('message', '')
                extra_data = kwargs.get('extra_data', {})
                
                if '阶段转换计算' in message:
                    print(f"✅ 转换计算日志: {message}")
                    
                    # 验证计算结果
                    market_value_159915 = extra_data.get('market_value_159915', 0)
                    target_510720_shares = extra_data.get('target_510720_shares', 0)
                    buy_strategy = extra_data.get('buy_strategy', '')
                    
                    expected_market_value = 2000 * 2.6  # 5200
                    expected_target_shares = int(expected_market_value / 1.3 / 100) * 100  # 4000股
                    
                    print(f"  159915市值: {market_value_159915} (预期: {expected_market_value})")
                    print(f"  目标510720份额: {target_510720_shares} (预期: {expected_target_shares})")
                    print(f"  买入策略: {buy_strategy}")
                    
                    if abs(market_value_159915 - expected_market_value) < 0.01:
                        print("✅ 市值计算正确")
                    else:
                        print("❌ 市值计算错误")
                        
                    if target_510720_shares == expected_target_shares:
                        print("✅ 转换份额计算正确")
                    else:
                        print("❌ 转换份额计算错误")
                        
                    if buy_strategy == "CONVERT_BY_SHARES":
                        print("✅ 现金充足，按份额买入策略正确")
                    else:
                        print(f"❌ 买入策略不符合预期: {buy_strategy}")
                        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()


def test_insufficient_cash_scenario():
    """测试现金不足场景"""
    print("\n" + "=" * 60)
    print("测试3: 现金不足场景")
    print("=" * 60)
    
    try:
        from value_averaging_strategy import execute_active_to_sleeping_transition_async
        import value_averaging_strategy
        
        # 重置模拟对象
        value_averaging_strategy.g_trade_task_queue = Mock()
        value_averaging_strategy.g_trade_task_queue.create_task_group = Mock(return_value="insufficient_cash_group")
        value_averaging_strategy.g_trade_task_queue.create_task = Mock(return_value=("task_id", "order_uuid"))
        value_averaging_strategy.g_trade_task_queue.log_task_message = Mock()
        value_averaging_strategy.g_trade_task_queue.calculate_fees = Mock(return_value=10.0)
        value_averaging_strategy.g_trade_task_queue.create_account_snapshot = Mock()
        
        # 模拟持仓、价格和账户信息（现金不足）
        with patch('value_averaging_strategy.get_current_position') as mock_position, \
             patch('value_averaging_strategy.get_current_price') as mock_price, \
             patch('value_averaging_strategy.get_account_info') as mock_account:
            
            # 设置模拟返回值
            mock_position.return_value = {'shares': 2000, 'avg_cost': 2.5, 'market_value': 5200}
            
            def price_side_effect(stock_code, context):
                if '159915' in stock_code:
                    return 2.6  # 159915价格
                elif '510720' in stock_code:
                    return 1.3  # 510720价格
                return 1.0
            
            mock_price.side_effect = price_side_effect
            mock_account.return_value = {'available_cash': 1000}  # 现金不足
            
            # 模拟信号详情和ContextInfo
            signal_details = {'signal_type': 'EXITLONG'}
            mock_context = Mock()
            
            # 执行测试
            task_group_id = execute_active_to_sleeping_transition_async(
                ContextInfo=mock_context,
                signal_details=signal_details
            )
            
            print(f"✅ 现金不足场景任务组ID: {task_group_id}")
            
            # 验证现金不足逻辑
            log_calls = value_averaging_strategy.g_trade_task_queue.log_task_message.call_args_list
            for call in log_calls:
                args, kwargs = call
                message = kwargs.get('message', '')
                extra_data = kwargs.get('extra_data', {})
                
                if '阶段转换计算' in message:
                    buy_strategy = extra_data.get('buy_strategy', '')
                    available_cash = extra_data.get('available_cash', 0)
                    total_needed_cash = extra_data.get('total_needed_cash', 0)
                    
                    print(f"  可用现金: {available_cash}")
                    print(f"  需要现金: {total_needed_cash}")
                    print(f"  买入策略: {buy_strategy}")
                    
                    if buy_strategy == "USE_ALL_CASH":
                        print("✅ 现金不足，用尽现金买入策略正确")
                    else:
                        print(f"❌ 现金不足场景买入策略错误: {buy_strategy}")
                        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("开始测试交易逻辑修改...")
    
    test_active_period_sell_logic()
    test_phase_transition_logic()
    test_insufficient_cash_scenario()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
