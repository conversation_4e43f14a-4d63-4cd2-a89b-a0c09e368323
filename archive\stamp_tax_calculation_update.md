# 印花税计算方式更新总结

## 修改目标

将印花税的获取方式从"在成交回调对象中查询"改为"使用SELL_TAX_RATE根据成交均价和数量计算"，确保费用计算的准确性和一致性。

## 主要修改内容

### 1. 费用计算常量升级

#### 新增费用率常量
```python
# 交易账户信息
ACCOUNT_ID = "************"       # 设置交易账户信息
ACCOUNT_TYPE = "CREDIT"           # 交易账户类型
COMMISSION_FEE_RATE = 0.0003      # 佣金费率（万分之3）
COMMISSION_FEE_MIN = 5            # 最低交易佣金（元）
SELL_TAX_RATE = 0.001             # 印花税率（千分之1，仅卖出）
TRANSFER_FEE_RATE = 0.00002       # 过户费率（万分之0.2，仅上海）
```

### 2. 统一费用计算函数

#### 新增calculate_trading_fees函数
```python
def calculate_trading_fees(amount: float, shares: int, trade_type: str, stock_code: str = None) -> dict:
    """计算交易费用"""
    # 佣金（买卖都收取）
    commission = max(amount * COMMISSION_FEE_RATE, COMMISSION_FEE_MIN)
    
    # 印花税（仅卖出时收取）
    stamp_tax = amount * SELL_TAX_RATE if trade_type == 'SELL' else 0.0
    
    # 过户费（根据股票代码判断）
    transfer_fee = 0.0
    if stock_code:
        # 上海股票（以6开头或者.SH结尾）收取过户费
        if stock_code.startswith('6') or stock_code.endswith('.SH'):
            transfer_fee = max(amount * TRANSFER_FEE_RATE, 1.0)  # 最低1元
        # 深圳股票（以0、2、3开头或者.SZ结尾）免收过户费
        elif stock_code.startswith(('0', '2', '3')) or stock_code.endswith('.SZ'):
            transfer_fee = 0.0
    
    total_fees = commission + stamp_tax + transfer_fee
    
    # 计算净金额
    if trade_type == 'BUY':
        net_amount = -(amount + total_fees)  # 买入总支出
    else:
        net_amount = amount - total_fees     # 卖出净收入
    
    return {
        'commission': commission,
        'stamp_tax': stamp_tax,
        'transfer_fee': transfer_fee,
        'total_fees': total_fees,
        'net_amount': net_amount,
        'gross_amount': amount
    }
```

### 3. 成交回调函数修改

#### 修改前（从回调对象查询）
```python
# 印花税（通常只有卖出时收取）
for attr in ['m_dStampTax', 'm_dTax', 'm_dStampDuty']:
    try:
        temp_tax = getattr(dealInfo, attr, 0.0)
        if temp_tax > 0:
            stamp_tax = temp_tax
            print(f"✓ 找到印花税属性：{attr} = {stamp_tax}")
            break
    except:
        continue
```

#### 修改后（根据税率计算）
```python
# 计算印花税（根据交易类型和税率计算）
stamp_tax = 0.0
trade_type = None

try:
    # 通过UUID查询交易类型
    cursor.execute("""
        SELECT trade_type FROM trade_execution_log 
        WHERE order_uuid = ? AND status = 'PENDING'
    """, (order_uuid,))
    result = cursor.fetchone()
    
    if result:
        trade_type = result[0]
        # 使用统一的费用计算函数，传入股票代码
        calculated_fees = calculate_trading_fees(deal_amount, deal_shares, trade_type, instrument_id)
        
        # 使用计算出的印花税和过户费
        stamp_tax = calculated_fees['stamp_tax']
        calculated_transfer_fee = calculated_fees['transfer_fee']
        
        if trade_type == 'SELL':
            print(f"✓ 计算印花税：{deal_amount:.2f} × {SELL_TAX_RATE*100}% = {stamp_tax:.2f}元（卖出）")
        else:
            print(f"✓ 印花税：{stamp_tax:.2f}元（买入免税）")
```

### 4. 过户费智能计算

#### 根据股票代码判断
```python
# 过户费（根据股票代码判断）
if stock_code:
    # 上海股票（以6开头或者.SH结尾）收取过户费
    if stock_code.startswith('6') or stock_code.endswith('.SH'):
        transfer_fee = max(amount * TRANSFER_FEE_RATE, 1.0)  # 最低1元
    # 深圳股票（以0、2、3开头或者.SZ结尾）免收过户费
    elif stock_code.startswith(('0', '2', '3')) or stock_code.endswith('.SZ'):
        transfer_fee = 0.0
```

### 5. 费用验证机制

#### 回调值与计算值对比
```python
# 验证佣金计算（可选）
calculated_commission = calculated_fees['commission']
if abs(deal_commission - calculated_commission) > 0.01:
    print(f"⚠️ 佣金差异：回调{deal_commission:.2f} vs 计算{calculated_commission:.2f}")

# 如果回调中没有过户费信息，使用计算值
if transfer_fee == 0.0 and calculated_transfer_fee > 0:
    transfer_fee = calculated_transfer_fee
    print(f"✓ 计算过户费：{deal_amount:.2f} × {TRANSFER_FEE_RATE*10000}‱ = {transfer_fee:.2f}元（{instrument_id}）")
```

## 费用计算规则

### 1. 印花税
- **税率**：0.1%（千分之1）
- **征收对象**：仅卖出时收取
- **计算公式**：`成交金额 × 0.001`
- **示例**：卖出245.00元，印花税 = 245.00 × 0.001 = 0.245元

### 2. 佣金
- **费率**：0.03%（万分之3）
- **最低收费**：5元
- **征收对象**：买卖都收取
- **计算公式**：`max(成交金额 × 0.0003, 5.0)`
- **示例**：交易245.00元，佣金 = max(245.00 × 0.0003, 5.0) = 5.00元

### 3. 过户费
- **费率**：0.002%（万分之0.2）
- **最低收费**：1元
- **征收对象**：仅上海股票
- **计算公式**：`max(成交金额 × 0.00002, 1.0)`（上海股票）
- **示例**：上海股票交易245.00元，过户费 = max(245.00 × 0.00002, 1.0) = 1.00元

## 预期运行效果

### 卖出交易示例
```
收到成交回调：UUID=abc-123，订单ID=807146084，股票=159915.SZ
  成交详情：100股 × 2.4500元 = 245.00元
✓ 计算印花税：245.00 × 0.1% = 0.25元（卖出）
✓ 找到手续费属性：m_dCommission = 5.00
  费用明细：佣金5.00元
           印花税0.25元（卖出税率0.1%）
  总费用：5.25元，净额：239.75元，时间：14:30:25

✓ 交易执行记录已更新：记录详情：均价2.4500，金额245.00，总费用5.25，净额239.75
✓ 费用明细已记录：佣金5.00，印花税0.25，过户费0.00
```

### 买入交易示例
```
收到成交回调：UUID=def-456，订单ID=807146085，股票=600000.SH
  成交详情：100股 × 24.20元 = 2420.00元
✓ 印花税：0.00元（买入免税）
✓ 计算过户费：2420.00 × 0.002% = 1.00元（600000.SH）
✓ 找到手续费属性：m_dCommission = 7.26
  费用明细：佣金7.26元
           印花税0.00元（买入免税）
           过户费1.00元
  总费用：8.26元，净额：-2428.26元，时间：09:30:15
```

## 技术优势

### 1. 计算准确性
- ✅ 使用标准税率，避免回调数据不准确
- ✅ 统一的计算逻辑，确保一致性
- ✅ 支持不同市场的费用规则

### 2. 代码可维护性
- ✅ 集中的费用计算函数
- ✅ 易于调整费用率参数
- ✅ 清晰的计算逻辑

### 3. 数据完整性
- ✅ 回调数据与计算数据双重验证
- ✅ 自动补充缺失的费用信息
- ✅ 详细的计算过程日志

### 4. 灵活性
- ✅ 支持不同股票代码的费用规则
- ✅ 可配置的费用率常量
- ✅ 易于扩展新的费用类型

这个修改使费用计算更加准确和可控，不再依赖可能不准确的回调数据，而是使用标准的市场费用率进行精确计算。
