#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库表是否正常工作
"""

import sqlite3
import datetime

# 数据库配置
DATABASE_PATH = "gytrading2.db"

def test_database_tables():
    """
    测试所有数据库表是否可以正常访问
    """
    try:
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        print("开始测试数据库表...")
        
        # 测试关键表
        tables_to_test = [
            'trade_execution_log',
            'trade_fee_details', 
            'order_status_history',
            'trade_logs',
            'strategy_status',
            'signal_history',
            'trade_orders',
            'position_records'
        ]
        
        for table in tables_to_test:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count} 条记录")
            except Exception as e:
                print(f"❌ {table}: 错误 - {str(e)}")
        
        # 测试插入 trade_execution_log 记录
        print("\n测试插入 trade_execution_log 记录...")
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        cursor.execute("""
            INSERT INTO trade_execution_log 
            (trade_time, trade_type, stock_code, shares, price, amount, fees, status, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, 'BUY', '159915', 100, 1.5, 150.0, 1.5, 'SUCCESS', current_time))
        
        # 测试查询
        cursor.execute("SELECT * FROM trade_execution_log WHERE stock_code = '159915'")
        records = cursor.fetchall()
        print(f"✅ 成功插入并查询到 {len(records)} 条 trade_execution_log 记录")
        
        # 测试插入 trade_fee_details 记录
        print("\n测试插入 trade_fee_details 记录...")
        cursor.execute("""
            INSERT INTO trade_fee_details 
            (order_uuid, commission, stamp_tax, transfer_fee, total_fees, net_amount, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, ('test-uuid-123', 0.5, 0.0, 0.0, 0.5, 149.5, current_time))
        
        cursor.execute("SELECT * FROM trade_fee_details WHERE order_uuid = 'test-uuid-123'")
        records = cursor.fetchall()
        print(f"✅ 成功插入并查询到 {len(records)} 条 trade_fee_details 记录")
        
        # 测试插入 order_status_history 记录
        print("\n测试插入 order_status_history 记录...")
        cursor.execute("""
            INSERT INTO order_status_history 
            (order_uuid, order_id, stock_code, order_status, status_desc, volume_traded, volume_total, callback_time, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, ('test-uuid-123', 'order-123', '159915', 1, '已成交', 100, 100, current_time, current_time))
        
        cursor.execute("SELECT * FROM order_status_history WHERE order_uuid = 'test-uuid-123'")
        records = cursor.fetchall()
        print(f"✅ 成功插入并查询到 {len(records)} 条 order_status_history 记录")
        
        # 清理测试数据
        print("\n清理测试数据...")
        cursor.execute("DELETE FROM trade_execution_log WHERE stock_code = '159915'")
        cursor.execute("DELETE FROM trade_fee_details WHERE order_uuid = 'test-uuid-123'")
        cursor.execute("DELETE FROM order_status_history WHERE order_uuid = 'test-uuid-123'")
        
        connection.commit()
        print("✅ 测试数据清理完成")
        
        cursor.close()
        connection.close()
        
        print("\n🎉 所有数据库表测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败：{str(e)}")
        return False

def main():
    """
    主函数
    """
    print("=" * 50)
    print("数据库表测试脚本")
    print("=" * 50)
    
    if test_database_tables():
        print("\n✅ 数据库测试成功！现在可以安全运行交易策略了。")
    else:
        print("\n❌ 数据库测试失败！")

if __name__ == "__main__":
    main()
