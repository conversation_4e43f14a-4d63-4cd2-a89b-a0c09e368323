# 激进版择时量化投资策略使用说明

## 策略概述

激进版策略是基于稳健版策略的简化版本，采用更直接的交易逻辑：
- **激活期**：一次性买入固定金额的ACTIVE_FUND_CODE
- **沉睡期**：全部卖出ACTIVE_FUND_CODE，买入等值的SLEEPING_FUND_CODE
- **每个期间只交易一次**，避免重复操作

## 核心特点

### 1. 信号判断逻辑
- 基于EMA35季线技术指标
- **激活期判断**：最近一次买卖信号是买入信号
- **沉睡期判断**：最近一次买卖信号是卖出信号

### 2. 交易执行逻辑
- **激活期交易**：
  - 条件：当前是激活期 + 本激活期内还没有买入过ACTIVE_FUND_CODE
  - 动作：买入固定金额的ACTIVE_FUND_CODE（现金不够就融资）
  
- **沉睡期交易**：
  - 条件：当前是沉睡期 + 数据库记录显示持有ACTIVE_FUND_CODE + 本沉睡期内还没有卖出过
  - 动作：全部卖出ACTIVE_FUND_CODE → 买入等值的SLEEPING_FUND_CODE

### 3. 关键参数
```python
# 基金代码配置
SLEEPING_FUND_CODE = "510720.SH"    # 沉睡期基金：国泰上证国有企业红利ETF
ACTIVE_FUND_CODE = "159967.SZ"      # 激活期基金：创业板ETF
SIGNAL_FUND_CODE = "159915.SZ"      # 信号检测基金：创业板ETF

# 激进版核心参数
AGGRESSIVE_INVESTMENT_AMOUNT = 100000  # 激进版固定投资金额（元）

# 技术指标参数(季线)
EMA_PERIOD = 35                 # EMA参数：默认35
BOTTOM_RATIO = 0.85             # 底部相对比例：默认0.85
TOP_RATIO = 1.90                # 顶部相对比例：默认1.90

# 交易账户信息
ACCOUNT_ID = "************"     # 设置交易账户信息
```

## 使用步骤

### 1. 参数配置
在`aggressive_strategy.py`文件顶部修改相关参数：
- 调整`AGGRESSIVE_INVESTMENT_AMOUNT`为您希望的投资金额
- 确认基金代码是否正确
- 设置正确的账户ID

### 2. 在iQuant平台加载策略
1. 将`aggressive_strategy.py`文件上传到iQuant平台
2. 在策略管理中选择该文件
3. 设置运行参数（如果需要）
4. 启动策略

### 3. 监控策略运行
- 策略会自动检测信号状态
- 在激活期/沉睡期切换时自动执行交易
- 所有交易记录会保存在数据库中

## 数据库结构

策略使用SQLite数据库记录以下信息：

### 1. signal_history（信号历史表）
- 记录所有买卖信号
- 用于判断当前是激活期还是沉睡期

### 2. trade_records（交易记录表）
- 记录所有交易操作
- 用于判断本期间是否已经交易过

### 3. holdings（持仓记录表）
- 记录基金持仓情况
- 用于沉睡期判断是否需要卖出

### 4. trade_logs（交易日志表）
- 记录策略运行日志
- 便于问题排查和性能分析

## 风险提示

### 1. 资金风险
- 策略会使用融资功能，请确保了解融资风险
- 固定投资金额可能超过账户可用资金

### 2. 市场风险
- 技术指标可能产生错误信号
- 市场极端情况下可能造成损失

### 3. 技术风险
- 网络中断可能影响交易执行
- 数据异常可能导致策略误判

## 与稳健版的区别

| 特性 | 稳健版 | 激进版 |
|------|--------|--------|
| 投资方式 | 价值平均策略，分期投入 | 一次性固定金额投入 |
| 交易频率 | 每个周期都可能调整 | 每个期间只交易一次 |
| 复杂度 | 复杂的任务队列管理 | 简化的直接交易 |
| 资金管理 | 精细的仓位控制 | 全仓买入/卖出 |
| 适用场景 | 长期稳健投资 | 快速响应信号变化 |

## 常见问题

### Q1: 如何修改投资金额？
A: 修改`AGGRESSIVE_INVESTMENT_AMOUNT`参数即可。

### Q2: 策略什么时候会交易？
A: 只有在信号状态发生变化且本期间还没有交易过时才会执行交易。

### Q3: 如何查看交易记录？
A: 可以查询数据库中的`trade_records`表，或查看策略运行日志。

### Q4: 融资额度不够怎么办？
A: 策略会先检查可用资金和融资额度，如果不够会记录错误日志并跳过交易。

### Q5: 如何停止策略？
A: 在iQuant平台中停止策略运行即可，已执行的交易不会自动撤销。

## 技术支持

如有问题，请检查：
1. 策略运行日志
2. 数据库中的错误记录
3. 账户登录状态
4. 网络连接状况

建议在实盘使用前先进行充分的回测验证。
