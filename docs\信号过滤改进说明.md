# 信号过滤功能改进说明

## 改进背景

原有的 `check_signal_filter` 方法存在逻辑问题：
- 基于时间间隔计算信号重复，而不是基于K线距离
- 不符合用户需求：同样的信号（买入或卖出），判定是否重复，应该与最近一次信号对应的K线与当前K线的距离

## 改进内容

### 1. 数据库结构升级

#### 新增字段
在 `signal_history` 表中新增两个字段：
```sql
ALTER TABLE signal_history ADD COLUMN kline_position INTEGER;  -- K线位置（barpos）
ALTER TABLE signal_history ADD COLUMN kline_date TEXT;         -- K线日期（YYYYMMDD格式）
```

#### 新增索引
```sql
CREATE INDEX idx_signal_history_kline_pos ON signal_history(kline_position);
```

#### 自动升级机制
添加了 `upgrade_database_schema()` 函数，自动检测并升级现有数据库结构，确保向后兼容。

### 2. 信号记录改进

#### 记录K线位置和日期
修改 `record_signal_to_db()` 函数，记录信号对应的K线位置和日期：
```python
cursor.execute("""
    INSERT INTO signal_history
    (signal_date, signal_type, signal_price, ema_value, bottom_line, top_line,
     kline_position, kline_date, is_valid, filter_reason, created_time)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
""", (..., signal_details.get('kline_position'), signal_details.get('kline_date'), ...))
```

#### 信号检测改进
修改 `detect_signals()` 函数：
- 获取当前K线位置：`current_kline_position = ContextInfo.barpos`
- 获取当前K线日期：通过 `ContextInfo.get_bar_timetag()` 获取时间戳并转换为YYYYMMDD格式
- 在信号详情中包含K线位置和日期信息
- 传递K线位置给过滤函数

```python
# 获取当前K线对应的日期（YYYYMMDD格式）
try:
    current_bar_timestamp = ContextInfo.get_bar_timetag(current_kline_position)
    if current_bar_timestamp:
        kline_datetime = datetime.datetime.fromtimestamp(current_bar_timestamp / 1000)
        current_kline_date = kline_datetime.strftime("%Y%m%d")
    else:
        current_kline_date = datetime.datetime.now().strftime("%Y%m%d")
except Exception as e:
    current_kline_date = datetime.datetime.now().strftime("%Y%m%d")
```

### 3. 过滤逻辑重写

#### 新的过滤算法
完全重写 `check_signal_filter()` 函数：

```python
def check_signal_filter(signal_type: str, signal_date: str, current_kline_position: int) -> Tuple[bool, str]:
    """
    基于K线距离进行信号过滤
    
    Args:
        signal_type: 信号类型 ('ENTERLONG' 或 'EXITLONG')
        signal_date: 信号日期
        current_kline_position: 当前K线位置
    
    Returns:
        tuple: (是否有效, 过滤原因)
    """
    # 1. 查询最近一次相同类型的有效信号
    cursor.execute("""
        SELECT signal_date, signal_type, kline_position FROM signal_history
        WHERE signal_type = ? AND is_valid = 1
        ORDER BY signal_date DESC
        LIMIT 1
    """, (signal_type,))
    
    # 2. 计算K线距离
    kline_distance = current_kline_position - last_kline_position
    
    # 3. 判断是否超过过滤周期
    if kline_distance <= filter_periods:
        return (False, f"距离上次{signal_type}信号仅{kline_distance}根K线，小于等于过滤周期{filter_periods}根K线")
    else:
        return (True, None)
```

#### 关键改进点
1. **基于K线距离**：不再使用时间间隔，而是直接计算K线位置差
2. **精确过滤**：买入信号8根K线内不重复，卖出信号10根K线内不重复
3. **分别判断**：买入和卖出信号分别维护各自的过滤状态
4. **向后兼容**：对于没有K线位置信息的历史记录，允许信号通过

## 测试验证

创建了完整的测试用例 `test_signal_filter.py`，验证以下场景：
1. ✅ 没有历史信号时，允许通过
2. ✅ 距离太近时（5根K线 < 8根），被过滤
3. ✅ 距离等于阈值时（8根K线 = 8根），被过滤
4. ✅ 距离超过阈值时（9根K线 > 8根），允许通过
5. ✅ 不同信号类型互不影响
6. ✅ 卖出信号的过滤阈值（10根K线）正确工作

## 配置参数

过滤周期在代码开头配置：
```python
# 信号过滤参数
BUY_SIGNAL_FILTER_PERIODS = 8   # 买入信号过滤周期：8个周期内不重复
SELL_SIGNAL_FILTER_PERIODS = 10 # 卖出信号过滤周期：10个周期内不重复
```

## 使用说明

### 对于新部署
直接使用新版本代码，数据库会自动创建包含 `kline_position` 字段的表结构。

### 对于现有部署
1. 运行策略时，`upgrade_database_schema()` 会自动检测并添加新字段
2. 新产生的信号会包含K线位置信息
3. 历史信号（没有K线位置）不会影响新信号的过滤逻辑

### 注意事项
1. K线位置基于 `ContextInfo.barpos`，确保在iQuant环境中正确获取
2. 过滤逻辑使用 `<=` 判断，即距离小于等于阈值时被过滤
3. 异常情况下（如数据库错误），默认允许信号通过，避免错过重要信号

## 改进效果

1. **精确控制**：基于K线距离的过滤更加精确，符合技术分析的逻辑
2. **性能优化**：只查询最近一条记录，而不是多条记录
3. **逻辑清晰**：过滤条件更加直观，易于理解和调试
4. **向后兼容**：现有数据库可以平滑升级，不影响历史数据
