# -*- coding: utf-8 -*-
"""
正确修复所有log_message调用
"""

import re

def fix_all_log_calls_properly():
    """正确修复所有log_message调用"""
    
    # 读取文件
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 先修复错误的str(e, None, ContextInfo)格式
    print("修复错误的str(e, None, ContextInfo)格式...")
    content = re.sub(r'str\(e, None, ContextInfo\)', 'str(e)', content)
    
    # 修复错误的type(xxx, None, ContextInfo)格式
    content = re.sub(r'type\([^,]+, None, ContextInfo\)', lambda m: m.group(0).replace(', None, ContextInfo', ''), content)
    
    # 修复错误的len(xxx, None, ContextInfo)格式
    content = re.sub(r'len\([^,]+, None, ContextInfo\)', lambda m: m.group(0).replace(', None, ContextInfo', ''), content)
    
    # 修复重复的ContextInfo参数
    content = re.sub(r', None, ContextInfo, None, ContextInfo\)', ', None, ContextInfo)', content)
    
    # 修复函数定义中的重复参数
    content = re.sub(r'def log_message\([^)]+, None, ContextInfo\):', 'def log_message(log_type: str, operation: str, message: str, details: Dict = None, ContextInfo=None):', content)
    
    # 找到所有仍然没有ContextInfo的log_message调用
    pattern = r'log_message\([^)]+\)(?![^)]*ContextInfo)'
    matches = list(re.finditer(pattern, content))
    
    print(f"找到 {len(matches)} 个仍需修复的log_message调用")
    
    # 从后往前替换，避免位置偏移
    for i, match in enumerate(reversed(matches)):
        old_call = match.group()
        
        # 检查是否是函数定义行
        line_start = content.rfind('\n', 0, match.start()) + 1
        line_end = content.find('\n', match.end())
        if line_end == -1:
            line_end = len(content)
        line_content = content[line_start:line_end]
        
        if 'def log_message(' in line_content:
            continue  # 跳过函数定义
        
        # 在最后一个参数后添加, None, ContextInfo
        if old_call.endswith(')'):
            new_call = old_call[:-1] + ', None, ContextInfo)'
        else:
            continue
        
        # 替换
        start_pos = match.start()
        end_pos = match.end()
        content = content[:start_pos] + new_call + content[end_pos:]
        
        if i % 20 == 0:  # 每20个打印一次进度
            print(f"已处理 {i+1}/{len(matches)} 个调用")
    
    print(f"完成！总共修复了 {len(matches)} 个log_message调用")
    
    # 写回文件
    with open('value_averaging_strategy.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("文件已更新！")
    
    return len(matches)

def main():
    """主函数"""
    print("开始正确修复所有log_message调用...")
    print("=" * 50)
    
    try:
        count = fix_all_log_calls_properly()
        print(f"\n✅ 成功修复了 {count} 个log_message调用")
        print("现在所有log_message调用都正确包含ContextInfo参数了！")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
