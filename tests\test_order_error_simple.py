#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的下单错误回调测试
"""

import sqlite3
import datetime
import uuid

DATABASE_PATH = "gytrading2.db"

def test_uuid_extraction():
    """测试UUID提取逻辑"""
    print("测试UUID提取逻辑...")
    
    test_cases = [
        ("价值平均策略_&&&_test-uuid-123", "test-uuid-123"),
        ("test-uuid-456", "test-uuid-456"),
        ("策略名_&&&_abc-def-789", "abc-def-789"),
        ("", "")
    ]
    
    for strategy_name, expected_uuid in test_cases:
        if '_&&&_' in strategy_name:
            extracted_uuid = strategy_name.split('_&&&_')[1]
        else:
            extracted_uuid = strategy_name
        
        if extracted_uuid == expected_uuid:
            print(f"✅ '{strategy_name}' -> '{extracted_uuid}'")
        else:
            print(f"❌ '{strategy_name}' -> '{extracted_uuid}' (期望: '{expected_uuid}')")

def test_database_operations():
    """测试数据库操作"""
    print("\n测试数据库操作...")
    
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        test_uuid = f"test-{str(uuid.uuid4())}"
        
        print(f"创建测试数据，UUID: {test_uuid}")
        
        # 创建 trade_task_queue 记录
        cursor.execute("""
            INSERT INTO trade_task_queue
            (task_group_id, task_type, stock_code, target_shares, target_amount,
             estimated_price, estimated_fees, task_status, order_uuid, order_id, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (str(uuid.uuid4()), "BUY_159915_CASH", "159915.SZ", 1000, 2500.0,
              2.5, 5.0, "WAITING_CALLBACK", test_uuid, "TEST12345", current_time))
        
        task_id = cursor.lastrowid
        print(f"创建任务记录，ID: {task_id}")
        
        # 创建 trade_orders 记录
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_ERROR", 1000,
              "PENDING", current_time))
        
        order_id = cursor.lastrowid
        print(f"创建订单记录，ID: {order_id}")
        
        # 模拟错误回调的更新操作
        error_msg = "测试错误：资金不足"
        
        # 1. 更新 trade_task_queue
        cursor.execute("""
            UPDATE trade_task_queue
            SET task_status = 'FAILED',
                error_message = ?
            WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
        """, (error_msg, test_uuid))
        
        task_updated = cursor.rowcount
        print(f"更新任务状态：{task_updated} 条记录")
        
        # 2. 更新 trade_orders（先查询再更新）
        cursor.execute("""
            SELECT id FROM trade_orders
            WHERE stock_code = ?
            AND target_shares = ?
            AND order_status = 'PENDING'
            AND created_time >= datetime('now', '-1 hour')
            ORDER BY id DESC
            LIMIT 1
        """, ("159915.SZ", 1000))

        order_to_update = cursor.fetchone()
        if order_to_update:
            order_update_id = order_to_update[0]
            cursor.execute("""
                UPDATE trade_orders
                SET order_status = 'FAILED',
                    error_message = ?
                WHERE id = ?
            """, (error_msg, order_update_id))
        
        order_updated = cursor.rowcount
        print(f"更新订单状态：{order_updated} 条记录")
        
        # 验证结果
        cursor.execute("""
            SELECT task_status, error_message FROM trade_task_queue 
            WHERE order_uuid = ?
        """, (test_uuid,))
        
        task_result = cursor.fetchone()
        if task_result:
            status, error = task_result
            print(f"任务状态验证：{status}, 错误信息：{error}")
            if status == "FAILED":
                print("✅ 任务状态更新成功")
            else:
                print("❌ 任务状态更新失败")
        
        cursor.execute("""
            SELECT order_status, error_message FROM trade_orders 
            WHERE stock_code = '159915.SZ' AND target_shares = 1000
            ORDER BY id DESC LIMIT 1
        """)
        
        order_result = cursor.fetchone()
        if order_result:
            status, error = order_result
            print(f"订单状态验证：{status}, 错误信息：{error}")
            if status == "FAILED":
                print("✅ 订单状态更新成功")
            else:
                print("❌ 订单状态更新失败")
        
        # 清理测试数据
        cursor.execute("DELETE FROM trade_task_queue WHERE order_uuid = ?", (test_uuid,))
        cursor.execute("DELETE FROM trade_orders WHERE id = ?", (order_id,))
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库操作测试完成")
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败：{str(e)}")
        import traceback
        traceback.print_exc()

def main():
    print("=" * 50)
    print("简化的下单错误回调测试")
    print("=" * 50)
    
    test_uuid_extraction()
    test_database_operations()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
