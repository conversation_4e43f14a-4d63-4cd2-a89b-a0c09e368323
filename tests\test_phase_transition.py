#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试阶段切换功能
验证从sleeping到active的切换是否正确设置start_period_date和start_period_price
以及是否正确记录交易订单
"""

import sqlite3
import datetime
import os
import sys

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_database():
    """创建测试数据库"""
    if os.path.exists('test_phase_transition.db'):
        os.remove('test_phase_transition.db')
    
    conn = sqlite3.connect('test_phase_transition.db')
    cursor = conn.cursor()
    
    # 创建策略状态表
    cursor.execute("""
        CREATE TABLE strategy_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            current_phase TEXT NOT NULL,
            last_check_time TEXT NOT NULL,
            first_activation_time TEXT,
            start_period_date TEXT,
            start_period_price REAL,
            current_period INTEGER DEFAULT 0,
            created_time TEXT NOT NULL,
            updated_time TEXT NOT NULL
        )
    """)
    
    # 创建交易订单表
    cursor.execute("""
        CREATE TABLE trade_orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_date TEXT NOT NULL,
            stock_code TEXT NOT NULL,
            order_type TEXT NOT NULL,
            order_reason TEXT NOT NULL,
            target_shares INTEGER NOT NULL,
            actual_shares INTEGER DEFAULT 0,
            actual_price REAL DEFAULT 0,
            order_status TEXT NOT NULL,
            error_message TEXT,
            execution_time TEXT,
            created_time TEXT NOT NULL
        )
    """)
    
    # 插入初始策略状态
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    cursor.execute("""
        INSERT INTO strategy_status
        (current_phase, last_check_time, created_time, updated_time)
        VALUES ('sleeping', ?, ?, ?)
    """, (current_time, current_time, current_time))
    
    conn.commit()
    return conn

def test_strategy_status_update():
    """测试策略状态更新功能"""
    print("=== 测试策略状态更新功能 ===")
    
    conn = create_test_database()
    cursor = conn.cursor()
    
    # 模拟策略状态更新
    def update_strategy_status_test(strategy_status, current_time):
        """测试版本的策略状态更新函数"""
        cursor.execute("""
            UPDATE strategy_status SET
            current_phase = ?, last_check_time = ?, first_activation_time = ?,
            start_period_date = ?, start_period_price = ?, current_period = ?,
            updated_time = ?
            WHERE id = 1
        """, (
            strategy_status['current_phase'],
            strategy_status['last_check_time'],
            strategy_status['first_activation_time'],
            strategy_status['start_period_date'],
            strategy_status['start_period_price'],
            strategy_status['current_period'],
            current_time
        ))
        conn.commit()
    
    # 测试用例1：从sleeping切换到active
    print("\n测试用例1：从sleeping切换到active")
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 模拟策略状态
    strategy_status = {
        'current_phase': 'active',
        'last_check_time': current_time,
        'first_activation_time': current_time,
        'start_period_date': '2025-08-07',
        'start_period_price': 1.2345,
        'current_period': 0
    }
    
    update_strategy_status_test(strategy_status, current_time)
    
    # 验证更新结果
    cursor.execute("SELECT * FROM strategy_status WHERE id = 1")
    result = cursor.fetchone()
    
    assert result[1] == 'active', f"阶段应为active，实际为{result[1]}"
    assert result[3] == current_time, f"首次激活时间应为{current_time}，实际为{result[3]}"
    assert result[4] == '2025-08-07', f"起始期日期应为2025-08-07，实际为{result[4]}"
    assert abs(result[5] - 1.2345) < 0.0001, f"起始期价格应为1.2345，实际为{result[5]}"
    
    print(f"策略状态更新成功：")
    print(f"  阶段：{result[1]}")
    print(f"  首次激活时间：{result[3]}")
    print(f"  起始期日期：{result[4]}")
    print(f"  起始期价格：{result[5]}")
    
    conn.close()
    
    # 清理
    if os.path.exists('test_phase_transition.db'):
        os.remove('test_phase_transition.db')
    
    print("✅ 策略状态更新测试通过")

def test_trade_order_recording():
    """测试交易订单记录功能"""
    print("\n=== 测试交易订单记录功能 ===")
    
    conn = create_test_database()
    cursor = conn.cursor()
    
    # 模拟交易订单记录
    def record_trade_order_test(stock_code, order_type, shares, order_reason):
        """测试版本的交易订单记录函数"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, created_time)
            VALUES (?, ?, ?, ?, ?, 'PENDING', ?)
        """, (current_time, stock_code, order_type, order_reason, shares, current_time))
        
        order_id = cursor.lastrowid
        conn.commit()
        return order_id
    
    # 测试用例1：记录买入订单
    print("\n测试用例1：记录买入订单")
    order_id = record_trade_order_test('159915', 'BUY', 1000, 'SIGNAL_BUY')
    
    # 验证记录
    cursor.execute("SELECT * FROM trade_orders WHERE id = ?", (order_id,))
    result = cursor.fetchone()
    
    assert result is not None, "应该能查询到交易订单记录"
    assert result[2] == '159915', f"股票代码应为159915，实际为{result[2]}"
    assert result[3] == 'BUY', f"订单类型应为BUY，实际为{result[3]}"
    assert result[5] == 1000, f"目标股数应为1000，实际为{result[5]}"
    assert result[8] == 'PENDING', f"订单状态应为PENDING，实际为{result[8]}"
    
    print(f"交易订单记录成功：")
    print(f"  订单ID：{result[0]}")
    print(f"  股票代码：{result[2]}")
    print(f"  订单类型：{result[3]}")
    print(f"  目标股数：{result[5]}")
    print(f"  订单状态：{result[8]}")
    
    # 测试用例2：记录卖出订单
    print("\n测试用例2：记录卖出订单")
    order_id2 = record_trade_order_test('510720', 'SELL', 500, 'SIGNAL_SELL')
    
    # 验证总记录数
    cursor.execute("SELECT COUNT(*) FROM trade_orders")
    count = cursor.fetchone()[0]
    assert count == 2, f"应该有2条交易记录，实际为{count}"
    
    conn.close()
    
    # 清理
    if os.path.exists('test_phase_transition.db'):
        os.remove('test_phase_transition.db')
    
    print("✅ 交易订单记录测试通过")

if __name__ == "__main__":
    test_strategy_status_update()
    test_trade_order_recording()
    print("\n🎉 所有阶段切换相关测试通过！")
