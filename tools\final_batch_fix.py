# -*- coding: utf-8 -*-
"""
最终批量修复剩余的log_message调用
"""

import re

def final_batch_fix():
    """最终批量修复剩余的log_message调用"""
    
    # 读取文件
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要修复的函数和它们是否有ContextInfo参数
    functions_with_context = {
        'update_technical_indicators': True,
        'execute_trading_logic': True,
        'execute_value_averaging_strategy': True,
        'has_adjusted_in_current_period': True,
        'is_period_adjustment_day': True,
        'is_last_trading_day_of_month': True,
        'update_last_adjustment_period': True,
        'calculate_current_period': True,
        'calculate_value_averaging': True,
        'execute_trade_order': True,
        'record_position_change': True,
        'simulate_position_update': True,
        'get_historical_highest_price': True,
        'is_month_end_trading_day': False,  # 这个函数没有ContextInfo参数
    }
    
    # 分行处理
    lines = content.split('\n')
    
    for i, line in enumerate(lines):
        # 跳过注释和函数定义
        if line.strip().startswith('#') or 'def log_message(' in line:
            continue
        
        # 查找需要修复的log_message调用
        if 'log_message(' in line and ', ContextInfo)' not in line and line.strip().endswith(')'):
            # 检查这一行是否在有ContextInfo的函数中
            function_name = None
            
            # 向上查找函数定义
            for j in range(i, -1, -1):
                if lines[j].strip().startswith('def '):
                    func_match = re.match(r'\s*def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', lines[j])
                    if func_match:
                        function_name = func_match.group(1)
                        break
            
            # 如果在有ContextInfo的函数中，添加ContextInfo参数
            if function_name in functions_with_context and functions_with_context[function_name]:
                if line.strip().endswith(')'):
                    lines[i] = line[:-1] + ', None, ContextInfo)'
                    print(f"修复第{i+1}行: {function_name}函数中的log_message调用")
    
    # 写回文件
    content = '\n'.join(lines)
    with open('value_averaging_strategy.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("批量修复完成！")

def main():
    """主函数"""
    print("开始最终批量修复...")
    print("=" * 40)
    
    try:
        final_batch_fix()
        print("✅ 批量修复完成！")
        
        # 再次验证
        print("\n验证修复结果...")
        with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找剩余的未修复调用
        lines = content.split('\n')
        remaining = 0
        
        for i, line in enumerate(lines, 1):
            if (line.strip().startswith('#') or 'def log_message(' in line):
                continue
            if 'log_message(' in line and ', ContextInfo)' not in line and line.strip().endswith(')'):
                remaining += 1
                print(f"第{i}行仍需修复: {line.strip()}")
        
        if remaining == 0:
            print("🎉 所有log_message调用都已修复完成！")
        else:
            print(f"⚠️  还有 {remaining} 个调用需要手动修复")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
