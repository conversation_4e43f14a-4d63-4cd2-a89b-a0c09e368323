# -*- coding: utf-8 -*-
"""
批量更新log_message调用的脚本
主要更新那些有ContextInfo可用的关键函数
"""

import re

def find_functions_with_contextinfo():
    """找到有ContextInfo参数的函数，这些函数中的log_message调用应该传入ContextInfo"""
    
    functions_with_context = [
        'execute_value_averaging_strategy',
        'execute_trade_order', 
        'execute_backtest_trade',
        'get_current_price',
        'switch_strategy_phase',
        'calculate_current_period',
        'update_last_adjustment_period',
        'get_historical_highest_price'
    ]
    
    return functions_with_context

def generate_update_suggestions():
    """生成更新建议"""
    
    print("=== log_message调用更新建议 ===")
    print()
    print("由于修改了log_message函数签名，需要更新关键函数中的调用。")
    print("好消息是：ContextInfo参数是可选的，所以现有调用不会报错。")
    print("但为了获得完整的K线时间记录，建议更新以下函数中的调用：")
    print()
    
    functions = find_functions_with_contextinfo()
    
    for i, func in enumerate(functions, 1):
        print(f"{i}. {func}函数中的log_message调用")
        print(f"   更新方式：在log_message调用末尾添加 , ContextInfo")
        print(f"   示例：log_message('INFO', '操作', '消息') -> log_message('INFO', '操作', '消息', None, ContextInfo)")
        print()
    
    print("=== 优先级建议 ===")
    print()
    print("🔥 高优先级（必须更新）：")
    print("- execute_value_averaging_strategy: 价值平均策略的核心日志")
    print("- execute_trade_order: 交易执行的关键日志")
    print("- switch_strategy_phase: 阶段切换的重要日志")
    print()
    print("⚡ 中优先级（建议更新）：")
    print("- get_current_price: 价格获取日志")
    print("- calculate_current_period: 期数计算日志")
    print()
    print("💡 低优先级（可选更新）：")
    print("- 其他函数中的log_message调用")
    print()
    
    print("=== 向后兼容性 ===")
    print()
    print("✅ 现有的log_message调用仍然有效")
    print("✅ 只是缺少K线时间信息（kline_date会使用系统时间）")
    print("✅ 不会导致程序崩溃或错误")
    print()
    
    print("=== 验证方法 ===")
    print()
    print("运行策略后，检查trade_logs表：")
    print("1. 有ContextInfo的调用：kline_date != log_date（回测模式下）")
    print("2. 无ContextInfo的调用：kline_date = log_date")
    print("3. is_backtest字段正确标识回测/实盘模式")

def create_test_query():
    """创建测试查询"""
    
    print("\n=== 测试查询SQL ===")
    print()
    print("-- 查看最近的日志记录，检查kline_date是否正确")
    print("SELECT ")
    print("    id,")
    print("    log_date,")
    print("    kline_date,")
    print("    operation,")
    print("    message,")
    print("    is_backtest,")
    print("    CASE ")
    print("        WHEN kline_date = log_date THEN '系统时间'")
    print("        ELSE 'K线时间'")
    print("    END as time_source")
    print("FROM trade_logs ")
    print("ORDER BY id DESC ")
    print("LIMIT 20;")
    print()
    
    print("-- 统计不同时间源的日志数量")
    print("SELECT ")
    print("    CASE ")
    print("        WHEN kline_date = log_date THEN '使用系统时间'")
    print("        ELSE '使用K线时间'")
    print("    END as time_source,")
    print("    is_backtest,")
    print("    COUNT(*) as count")
    print("FROM trade_logs ")
    print("GROUP BY time_source, is_backtest;")

def main():
    """主函数"""
    print("log_message函数优化 - 调用更新指南")
    print("=" * 50)
    
    generate_update_suggestions()
    create_test_query()
    
    print("\n=== 总结 ===")
    print()
    print("✅ 已修复log_message函数，支持K线时间记录")
    print("✅ 保持向后兼容，现有调用不会出错")
    print("✅ 新增kline_date和is_backtest字段")
    print("✅ 提供查询函数便于分析")
    print()
    print("📝 下一步：")
    print("1. 运行策略，验证日志记录正常")
    print("2. 逐步更新关键函数中的log_message调用")
    print("3. 使用查询函数分析日志数据")
    print()
    print("🎯 目标：回测模式下能按K线时间查询和分析日志")

if __name__ == "__main__":
    main()
