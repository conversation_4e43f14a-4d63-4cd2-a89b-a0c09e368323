# UUID匹配机制修复总结

## 问题背景

原有的订单匹配机制存在时差问题：
1. 调用 `passorder` 下单
2. 立即调用 `get_last_order_id` 获取订单号
3. 由于时差，可能获取到上一次交易的订单号，导致订单错乱

## 解决方案

使用 `passorder` 的第10个参数（备注字段）传入UUID，在回调函数中通过 `m_strRemark` 字段匹配，确保订单不会错乱。

## 主要修改内容

### 1. 数据库结构修改

#### trade_task_queue 表
```sql
-- 添加 order_uuid 字段
ALTER TABLE trade_task_queue ADD COLUMN order_uuid TEXT;
```

#### trade_execution_log 表
```sql
-- 添加 order_uuid 字段
ALTER TABLE trade_execution_log ADD COLUMN order_uuid TEXT;
```

### 2. 任务创建流程修改

#### 创建任务时生成UUID
```python
def create_trade_task(self, task_group_id: str, task_type: str, stock_code: str, 
                     target_shares: int, reason: str) -> tuple:
    # 生成订单UUID用于回调匹配
    order_uuid = str(uuid.uuid4())
    
    # 存储到数据库
    cursor.execute("""
        INSERT INTO trade_task_queue
        (..., order_uuid, ...)
        VALUES (..., ?, ...)
    """, (..., order_uuid, ...))
    
    return task_id, order_uuid
```

### 3. 下单流程修改

#### 使用UUID作为备注
```python
def place_buy_order(self, stock_code: str, shares: int, order_uuid: str, ContextInfo):
    result = passorder(
        33,  # 买入
        1101,
        ACCOUNT_ID,
        stock_code,
        44,  # 市价
        -1,  # 市价
        shares,
        '简单交易测试策略',
        1,
        order_uuid,  # 使用UUID作为备注，用于回调匹配
        ContextInfo
    )
    
    # 返回临时ID，真正的订单ID会在回调中获取
    return f"TEMP_BUY_{order_uuid[:8]}"
```

### 4. 回调函数修改

#### 订单回调函数
```python
def order_callback(ContextInfo, orderInfo):
    # 获取订单UUID（从备注字段）
    order_uuid = str(getattr(orderInfo, 'm_strRemark', ''))
    
    # 获取真实订单ID
    order_id = str(getattr(orderInfo, 'm_strOrderSysID', ''))
    
    # 更新任务中的真实订单ID
    cursor.execute("""
        UPDATE trade_task_queue 
        SET order_id = ?
        WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
    """, (order_id, order_uuid))
    
    # 通过UUID匹配任务
    if order_status == 56:  # 已成
        cursor.execute("""
            UPDATE trade_task_queue 
            SET task_status = 'COMPLETED', completed_time = ?
            WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
        """, (current_time, order_uuid))
```

#### 成交回调函数
```python
def deal_callback(ContextInfo, dealInfo):
    # 获取订单UUID（从备注字段）
    order_uuid = str(getattr(dealInfo, 'm_strRemark', ''))
    
    # 通过UUID匹配执行记录
    cursor.execute("""
        UPDATE trade_execution_log 
        SET order_id = ?, price = ?, amount = ?, status = 'SUCCESS'
        WHERE order_uuid = ? AND status = 'PENDING'
    """, (order_id, deal_price, deal_amount, order_uuid))
```

### 5. 交易频率控制修改

#### 从固定休眠改为每日一次
- **原来**：交易后休眠3600秒
- **现在**：每天最多交易一次

```python
# 全局变量
g_today_traded = False      # 今天是否已经交易过
g_last_trade_date = None    # 最后交易日期

def should_trade_today() -> bool:
    """检查今天是否可以交易"""
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    return not g_today_traded and g_last_trade_date != current_date

def mark_today_traded():
    """标记今天已经交易"""
    global g_today_traded, g_last_trade_date
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    g_today_traded = True
    g_last_trade_date = current_date
```

## 工作流程

### 1. 任务创建
1. 生成UUID：`order_uuid = str(uuid.uuid4())`
2. 存储到数据库：`trade_task_queue.order_uuid = order_uuid`
3. 返回：`(task_id, order_uuid)`

### 2. 订单执行
1. 调用 `passorder`，第10个参数传入 `order_uuid`
2. 返回临时订单ID：`TEMP_BUY_{order_uuid[:8]}`
3. 任务状态：`WAITING_CALLBACK`

### 3. 订单回调
1. 从 `m_strRemark` 获取 `order_uuid`
2. 从 `m_strOrderSysID` 获取真实 `order_id`
3. 更新数据库：`order_uuid` → `order_id`
4. 如果已成交，标记任务完成

### 4. 成交回调
1. 从 `m_strRemark` 获取 `order_uuid`
2. 更新执行记录：价格、金额、状态
3. 备用：同时更新任务状态

## 优势

### 1. 解决时差问题
- ✅ 不再依赖 `get_last_order_id`
- ✅ 通过UUID精确匹配订单
- ✅ 避免订单错乱

### 2. 提高可靠性
- ✅ 双重更新机制（订单回调 + 成交回调）
- ✅ 详细的调试信息
- ✅ 完整的错误处理

### 3. 简化交易控制
- ✅ 每天最多交易一次
- ✅ 不需要复杂的休眠逻辑
- ✅ 状态持久化

## 预期效果

### 修复前的问题
```
买入订单已提交：159915.SZ 100股，订单号：-1
收到订单回调：订单ID=，状态=56
收到成交回调：订单ID=，成交0股，价格0.0000，金额0.00
```

### 修复后的预期
```
✓ 交易任务已创建：任务组ID=xxx，任务ID=1，订单UUID=abc-def-123
买入订单已提交：159915.SZ 100股，订单号：TEMP_BUY_abc-def，UUID：abc-def-123
收到订单回调：UUID=abc-def-123，订单ID=807146084，状态=56
✓ 已更新任务的真实订单ID：UUID=abc-def-123，订单ID=807146084
收到成交回调：UUID=abc-def-123，订单ID=807146084，成交100股，价格=2.4500，金额=245.00
✓ 交易执行记录已更新：UUID=abc-def-123，订单ID=807146084
✓ 任务已标记完成：UUID=abc-def-123
```

## 测试要点

1. **UUID匹配**：确认回调中能正确获取UUID
2. **订单ID更新**：确认真实订单ID被正确更新
3. **状态同步**：确认任务和执行记录状态正确更新
4. **每日交易控制**：确认每天只交易一次

这个修复方案彻底解决了订单匹配的时差问题，提高了系统的可靠性和准确性。
