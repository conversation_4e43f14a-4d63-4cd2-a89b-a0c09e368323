# -*- coding: utf-8 -*-
"""
批量更新log_message调用的脚本
"""

import re

def update_log_calls_in_file():
    """批量更新文件中的log_message调用"""
    
    # 读取文件
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要更新的函数列表（这些函数有ContextInfo参数）
    functions_with_context = [
        'execute_phase_transition',
        'switch_strategy_phase', 
        'calculate_current_period',
        'update_last_adjustment_period',
        'get_historical_highest_price',
        'record_position_change',
        'simulate_position_update',
        'execute_buy_order',
        'execute_normal_buy',
        'execute_margin_buy',
        'execute_sell_order',
        'get_account_info',
        'get_all_positions'
    ]
    
    # 统计更新情况
    total_updates = 0
    
    for func_name in functions_with_context:
        # 找到函数定义
        func_pattern = rf'def {func_name}\([^)]*ContextInfo[^)]*\):'
        func_match = re.search(func_pattern, content)
        
        if func_match:
            print(f"找到函数: {func_name}")
            
            # 找到函数的结束位置（简化处理，找到下一个def或文件结尾）
            start_pos = func_match.start()
            
            # 找下一个函数定义或文件结尾
            next_func_pattern = r'\ndef [a-zA-Z_][a-zA-Z0-9_]*\('
            next_func_match = re.search(next_func_pattern, content[start_pos + 100:])
            
            if next_func_match:
                end_pos = start_pos + 100 + next_func_match.start()
            else:
                end_pos = len(content)
            
            # 提取函数内容
            func_content = content[start_pos:end_pos]
            
            # 在函数内容中查找log_message调用
            log_pattern = r'log_message\([^)]+\)'
            log_matches = list(re.finditer(log_pattern, func_content))
            
            print(f"  找到 {len(log_matches)} 个log_message调用")
            
            # 从后往前替换，避免位置偏移
            for match in reversed(log_matches):
                old_call = match.group()
                
                # 检查是否已经有ContextInfo参数
                if ', ContextInfo)' not in old_call:
                    # 在最后一个参数后添加ContextInfo
                    if old_call.endswith(')'):
                        # 检查是否已经有details参数
                        if old_call.count(',') >= 2:  # log_type, operation, message至少3个参数
                            new_call = old_call[:-1] + ', None, ContextInfo)'
                        else:
                            new_call = old_call[:-1] + ', None, ContextInfo)'
                        
                        # 替换函数内容
                        func_start_in_content = start_pos + match.start()
                        func_end_in_content = start_pos + match.end()
                        
                        content = content[:func_start_in_content] + new_call + content[func_end_in_content:]
                        total_updates += 1
                        
                        print(f"    更新: {old_call} -> {new_call}")
    
    print(f"\n总共更新了 {total_updates} 个log_message调用")
    
    # 写回文件
    with open('value_averaging_strategy_updated.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("已保存到 value_averaging_strategy_updated.py")
    
    return total_updates

def show_remaining_functions():
    """显示还需要手动更新的函数"""
    
    print("\n=== 还需要手动更新的函数 ===")
    
    # 这些函数中的log_message调用需要手动更新
    manual_functions = [
        'execute_phase_transition',  # 阶段切换 - 很多log_message调用
        'calculate_value_averaging',  # 价值平均计算
        'has_adjusted_in_current_period',  # 重复检查
        'is_period_adjustment_day',  # 调整日判断
        'is_last_trading_day_of_month',  # 月末判断
    ]
    
    for func in manual_functions:
        print(f"- {func}: 需要手动添加ContextInfo参数到log_message调用")
    
    print("\n手动更新示例:")
    print("log_message('INFO', '操作', '消息')")
    print("↓")
    print("log_message('INFO', '操作', '消息', None, ContextInfo)")

def main():
    """主函数"""
    print("批量更新log_message调用")
    print("=" * 40)
    
    try:
        updates = update_log_calls_in_file()
        
        if updates > 0:
            print(f"\n✅ 成功更新了 {updates} 个log_message调用")
            print("📁 更新后的文件保存为: value_averaging_strategy_updated.py")
            print("\n下一步:")
            print("1. 检查更新后的文件")
            print("2. 如果没问题，替换原文件")
            print("3. 手动更新剩余的函数")
        else:
            print("\n❌ 没有找到需要更新的调用")
        
        show_remaining_functions()
        
    except Exception as e:
        print(f"❌ 更新失败: {str(e)}")

if __name__ == "__main__":
    main()
