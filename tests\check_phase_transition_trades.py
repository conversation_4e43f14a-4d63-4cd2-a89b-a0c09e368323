#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查阶段切换时的交易记录
验证从激活期到沉睡期是否有买入510720的记录
"""

import sqlite3
import os

# 数据库配置
DATABASE_PATH = "database/gytrading2.db"

def check_phase_transition_trades():
    """
    检查阶段切换相关的交易记录
    """
    if not os.path.exists(DATABASE_PATH):
        print(f"❌ 数据库文件不存在: {DATABASE_PATH}")
        return
    
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        print("=" * 60)
        print("阶段切换交易记录检查")
        print("=" * 60)
        
        # 1. 检查所有交易记录
        print("\n📊 所有交易记录:")
        cursor.execute("""
            SELECT order_date, stock_code, order_type, order_reason, 
                   target_shares, actual_shares, actual_price, order_status,
                   created_time
            FROM trade_orders 
            ORDER BY created_time DESC
        """)
        
        all_trades = cursor.fetchall()
        if all_trades:
            print(f"总共 {len(all_trades)} 条交易记录:")
            for trade in all_trades:
                date, code, type_, reason, target, actual, price, status, created = trade
                print(f"  {created}: {type_} {code} {actual}股 @ {price:.4f} ({reason}) - {status}")
        else:
            print("  无交易记录")
        
        # 2. 检查159915相关交易
        print("\n🎯 159915 (创业板ETF) 交易记录:")
        cursor.execute("""
            SELECT order_date, order_type, order_reason, 
                   target_shares, actual_shares, actual_price, order_status,
                   created_time
            FROM trade_orders 
            WHERE stock_code = '159915.SZ'
            ORDER BY created_time DESC
        """)
        
        trades_159915 = cursor.fetchall()
        if trades_159915:
            print(f"159915 共 {len(trades_159915)} 条交易:")
            for trade in trades_159915:
                date, type_, reason, target, actual, price, status, created = trade
                print(f"  {created}: {type_} {actual}股 @ {price:.4f} ({reason}) - {status}")
        else:
            print("  无159915交易记录")
        
        # 3. 检查510720相关交易
        print("\n🏦 510720 (红利国企ETF) 交易记录:")
        cursor.execute("""
            SELECT order_date, order_type, order_reason, 
                   target_shares, actual_shares, actual_price, order_status,
                   created_time
            FROM trade_orders 
            WHERE stock_code = '510720.SH'
            ORDER BY created_time DESC
        """)
        
        trades_510720 = cursor.fetchall()
        if trades_510720:
            print(f"510720 共 {len(trades_510720)} 条交易:")
            for trade in trades_510720:
                date, type_, reason, target, actual, price, status, created = trade
                print(f"  {created}: {type_} {actual}股 @ {price:.4f} ({reason}) - {status}")
        else:
            print("  ❌ 无510720交易记录")
        
        # 4. 检查信号相关的交易
        print("\n📡 信号相关交易 (SIGNAL_BUY/SIGNAL_SELL):")
        cursor.execute("""
            SELECT order_date, stock_code, order_type, order_reason, 
                   target_shares, actual_shares, actual_price, order_status,
                   created_time
            FROM trade_orders 
            WHERE order_reason IN ('SIGNAL_BUY', 'SIGNAL_SELL')
            ORDER BY created_time DESC
        """)
        
        signal_trades = cursor.fetchall()
        if signal_trades:
            print(f"信号交易 共 {len(signal_trades)} 条:")
            for trade in signal_trades:
                date, code, type_, reason, target, actual, price, status, created = trade
                print(f"  {created}: {type_} {code} {actual}股 @ {price:.4f} ({reason}) - {status}")
        else:
            print("  无信号相关交易记录")
        
        # 5. 检查策略状态变化
        print("\n🔄 策略状态记录:")
        cursor.execute("""
            SELECT current_phase, last_check_time, first_activation_time,
                   start_period_date, current_period, last_adjustment_time,
                   created_time, updated_time
            FROM strategy_status 
            ORDER BY updated_time DESC
        """)
        
        status_records = cursor.fetchall()
        if status_records:
            print(f"策略状态 共 {len(status_records)} 条记录:")
            for record in status_records:
                phase, last_check, first_act, start_date, period, last_adj, created, updated = record
                print(f"  {updated}: 阶段={phase}, 期数={period}, 首次激活={first_act}")
        else:
            print("  无策略状态记录")
        
        # 6. 检查信号历史
        print("\n📈 信号历史记录:")
        cursor.execute("""
            SELECT signal_date, signal_type, signal_price, is_valid, 
                   filter_reason, created_time
            FROM signal_history 
            ORDER BY signal_date DESC
            LIMIT 10
        """)
        
        signals = cursor.fetchall()
        if signals:
            print(f"最近 {len(signals)} 条信号:")
            for signal in signals:
                date, type_, price, valid, reason, created = signal
                status = "有效" if valid else f"无效({reason})"
                print(f"  {date}: {type_} @ {price:.4f} - {status}")
        else:
            print("  无信号记录")
        
        # 7. 分析问题
        print("\n🔍 问题分析:")
        
        # 检查是否有EXITLONG信号但没有对应的510720买入
        cursor.execute("""
            SELECT COUNT(*) FROM signal_history 
            WHERE signal_type = 'EXITLONG' AND is_valid = 1
        """)
        exitlong_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM trade_orders 
            WHERE stock_code = '510720.SH' AND order_type = 'BUY'
        """)
        buy_510720_count = cursor.fetchone()[0]
        
        print(f"  有效EXITLONG信号数量: {exitlong_count}")
        print(f"  510720买入交易数量: {buy_510720_count}")
        
        if exitlong_count > 0 and buy_510720_count == 0:
            print("  ⚠️ 发现问题: 有EXITLONG信号但没有510720买入记录")
            print("  可能原因:")
            print("    1. 阶段切换逻辑中买入510720失败")
            print("    2. 可用资金不足")
            print("    3. 510720价格获取失败")
            print("    4. 计算的买入股数小于最小交易单位")
        elif exitlong_count == 0:
            print("  ℹ️ 没有有效的EXITLONG信号，所以没有触发阶段切换")
        else:
            print("  ✅ EXITLONG信号和510720买入记录数量匹配")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")

def main():
    """
    主函数
    """
    check_phase_transition_trades()

if __name__ == "__main__":
    main()
