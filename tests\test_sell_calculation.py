# -*- coding: utf-8 -*-
"""
测试卖出股数计算问题
"""

def test_sell_calculation():
    """测试2020-02-28的卖出计算"""
    
    print("🔍 测试2020-02-28卖出计算")
    print("=" * 50)
    
    # 2020-02-28的数据
    period = 57
    target_amount = 57 * 10000  # 570000元
    current_price = 1.995
    
    # 从Excel获取的正确数据
    excel_current_shares = 323700  # Excel显示的当前持仓
    excel_sell_shares = 37985      # Excel计算的应卖出股数
    
    # 从数据库获取的错误数据  
    db_sell_shares = 32973         # 数据库记录的实际卖出股数
    
    print(f"📊 基础数据:")
    print(f"期数: {period}")
    print(f"目标金额: {target_amount:,}元")
    print(f"当前价格: {current_price}元")
    print(f"当前持仓: {excel_current_shares:,}股")
    
    # 计算当前价值
    current_value = excel_current_shares * current_price
    print(f"当前价值: {current_value:,.2f}元")
    
    # 计算需要调整的金额
    trade_amount = target_amount - current_value
    print(f"需要调整: {trade_amount:,.2f}元")
    
    if trade_amount < 0:
        print(f"需要卖出: {abs(trade_amount):,.2f}元")
        
        # 按照当前算法计算
        sell_amount = abs(trade_amount)
        calculated_shares = int(sell_amount / current_price)
        calculated_shares = max(100, calculated_shares)  # 最少100股
        
        print(f"\n🧮 算法计算:")
        print(f"计算卖出股数: int({sell_amount:.2f} / {current_price}) = {calculated_shares}股")
        
        # 检查是否超过持仓
        if calculated_shares > excel_current_shares:
            print(f"⚠️  计算股数{calculated_shares}超过持仓{excel_current_shares}，应调整为{excel_current_shares}")
            final_shares = excel_current_shares
        else:
            final_shares = calculated_shares
            
        print(f"最终卖出股数: {final_shares}股")
        
        # 对比结果
        print(f"\n📋 结果对比:")
        print(f"Excel正确计算: {excel_sell_shares}股")
        print(f"算法计算结果: {final_shares}股")
        print(f"数据库实际记录: {db_sell_shares}股")
        
        # 分析差异
        if final_shares == excel_sell_shares:
            print(f"✅ 算法计算正确")
        else:
            print(f"❌ 算法计算错误，差异: {abs(final_shares - excel_sell_shares)}股")
            
        if db_sell_shares != excel_sell_shares:
            print(f"❌ 数据库记录错误，差异: {abs(db_sell_shares - excel_sell_shares)}股")
            
            # 分析可能的原因
            print(f"\n🔍 差异分析:")
            
            # 检查是否是持仓限制导致的
            if db_sell_shares < excel_sell_shares:
                # 反推当时的持仓数量
                implied_position = db_sell_shares  # 如果卖出股数被限制为持仓数量
                print(f"可能原因1: 当时持仓只有{implied_position}股，不足以卖出{excel_sell_shares}股")
                
                # 检查是否是计算精度问题
                precise_shares = sell_amount / current_price
                print(f"精确计算: {sell_amount:.2f} / {current_price} = {precise_shares:.2f}股")
                print(f"取整后: {int(precise_shares)}股")
                
                if int(precise_shares) == db_sell_shares:
                    print(f"可能原因2: 直接取整，没有考虑最小交易单位")
                    
    return {
        'excel_correct': excel_sell_shares,
        'algorithm_result': final_shares,
        'database_actual': db_sell_shares,
        'is_algorithm_correct': final_shares == excel_sell_shares,
        'is_database_correct': db_sell_shares == excel_sell_shares
    }

def main():
    """主函数"""
    print("开始测试卖出计算...")
    
    try:
        result = test_sell_calculation()
        
        print(f"\n🎯 测试结果:")
        print(f"算法是否正确: {'✅' if result['is_algorithm_correct'] else '❌'}")
        print(f"数据库是否正确: {'✅' if result['is_database_correct'] else '❌'}")
        
        if not result['is_database_correct']:
            print(f"\n🔧 需要修复的问题:")
            print(f"1. 检查持仓获取逻辑是否正确")
            print(f"2. 检查卖出股数计算是否有额外限制")
            print(f"3. 检查是否有精度丢失问题")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
