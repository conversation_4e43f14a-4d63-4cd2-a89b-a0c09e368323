#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试成交回调修复的脚本
模拟真实的成交回调流程，验证数据是否正确插入到各个表中
"""

import sqlite3
import datetime
import uuid
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略模块
from value_averaging_strategy import (
    g_trade_task_callback_handler, 
    g_db_connection,
    init_database
)

# 数据库配置
DATABASE_PATH = "gytrading2.db"

class MockDealInfo:
    """模拟成交信息对象"""
    def __init__(self, shares, price, amount):
        self.m_nDealVol = shares      # 成交股数
        self.m_dDealPrice = price     # 成交价格
        self.m_dDealAmount = amount   # 成交金额

def setup_test_task():
    """创建测试任务"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 创建测试任务组
        task_group_id = str(uuid.uuid4())
        order_uuid = str(uuid.uuid4())
        
        print(f"📝 创建测试任务组：{task_group_id}")
        print(f"📝 创建订单UUID：{order_uuid}")
        
        # 插入测试任务
        cursor.execute("""
            INSERT INTO trade_task_queue
            (task_group_id, task_type, stock_code, target_shares, target_amount,
             estimated_price, estimated_fees, task_status, order_uuid, order_id, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (task_group_id, "BUY_159915_CASH", "159915.SZ", 1000, 2500.0,
              2.5, 5.0, "WAITING_CALLBACK", order_uuid, "12345", current_time))
        
        task_id = cursor.lastrowid
        
        # 插入对应的 trade_orders 记录
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_VALUE_AVERAGE", 1000,
              "PENDING", current_time))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 测试任务创建成功，任务ID={task_id}, UUID={order_uuid}")
        return task_id, order_uuid, task_group_id
        
    except Exception as e:
        print(f"❌ 创建测试任务失败：{str(e)}")
        return None, None, None

def check_table_data():
    """检查各个表的数据"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        print("\n📊 检查各表数据：")
        
        tables_to_check = [
            'trade_orders', 'trade_execution_log', 'trade_fee_details', 
            'position_records', 'account_info'
        ]
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  {table}: {count} 条记录")
                
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table} ORDER BY id DESC LIMIT 1")
                    latest_record = cursor.fetchone()
                    print(f"    最新记录: {latest_record}")
            except Exception as e:
                print(f"  {table}: 查询失败 - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据失败：{str(e)}")

def simulate_deal_callback():
    """模拟成交回调"""
    try:
        print("🧪 开始模拟成交回调测试...")
        
        # 1. 初始化数据库
        print("📝 初始化数据库...")
        init_database()
        
        # 2. 创建测试任务
        task_id, order_uuid, task_group_id = setup_test_task()
        if not task_id:
            return False
        
        # 3. 检查初始状态
        print("\n📊 初始状态：")
        check_table_data()
        
        # 4. 模拟成交信息
        deal_shares = 1000
        deal_price = 2.5
        deal_amount = deal_shares * deal_price
        
        mock_deal_info = MockDealInfo(deal_shares, deal_price, deal_amount)
        
        print(f"\n💰 模拟成交信息：")
        print(f"  成交股数：{deal_shares}")
        print(f"  成交价格：{deal_price}")
        print(f"  成交金额：{deal_amount}")
        
        # 5. 查找任务并处理成交回调
        print(f"\n🔄 开始处理成交回调...")
        
        # 模拟查找任务的过程
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT * FROM trade_task_queue 
            WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
        """, (order_uuid,))
        
        result = cursor.fetchone()
        if result:
            columns = [desc[0] for desc in cursor.description]
            task = dict(zip(columns, result))
            
            print(f"✅ 找到匹配任务：ID={task['id']}")
            
            # 调用成交回调处理
            g_trade_task_callback_handler.process_deal_callback_with_uuid(task, mock_deal_info, order_uuid, "12345")
            
        else:
            print("❌ 未找到匹配的任务")
            conn.close()
            return False
        
        conn.close()
        
        # 6. 检查处理后的状态
        print(f"\n📊 处理后状态：")
        check_table_data()
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟成交回调失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("成交回调修复测试")
    print("=" * 60)
    
    if simulate_deal_callback():
        print("\n✅ 测试成功！成交回调数据插入验证通过")
    else:
        print("\n❌ 测试失败！需要进一步检查")
    
    print("\n" + "=" * 60)
