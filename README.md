# GYTrading2 - 量化交易策略项目

## 项目结构

### 核心文件
- `value_averaging_strategy.py` - 主策略：价值平均+择时量化投资策略
- `simple_trading_test.py` - 简单交易测试策略
- `simple_ma_crossover_strategy.py` - 简单移动平均线策略

### 文档目录
- `价值平均策略实现分析.md` - 价值平均策略详细分析
- `回测模式使用说明.md` - 回测功能使用指南
- `回测适配方案.md` - 回测适配技术方案
- `简单移动平均线策略使用说明.md` - 移动平均线策略说明

### 目录结构

#### `/docs/` - 项目文档
- 功能说明文档
- 技术设计文档
- 使用指南
- iQuant平台资料

#### `/tests/` - 测试脚本
- 单元测试
- 功能验证脚本
- 调试工具
- 性能测试

#### `/tools/` - 工具脚本
- 数据修复工具
- 批量处理脚本
- 代码更新工具
- 维护脚本

#### `/database/` - 数据库相关
- 数据库初始化脚本
- 数据库文件 (`gytrading2.db`)
- 数据库维护工具

#### `/archive/` - 历史文档
- 开发总结
- 修复记录
- 升级说明
- 技术分析

## 主要策略说明

### 价值平均策略 (`value_averaging_strategy.py`)
- **信号检测**：基于创业板ETF(159915)的季度线EMA指标
- **择时逻辑**：EMA突破底部线买入，突破顶部线卖出
- **仓位管理**：沉睡期持有红利国企ETF(510720)，激活期持有创业板ETF(159915)
- **定投方式**：按月进行价值平均调整
- **风险控制**：多重信号过滤，防重复交易

### 简单交易测试策略 (`simple_trading_test.py`)
- **目标**：测试交易执行逻辑
- **标的**：159915.SZ (易方达创业板ETF)
- **规则**：每次买卖100股，交易后等待3600秒
- **用途**：验证交易接口和异步队列系统

## 使用说明

### 回测模式
1. 设置 `IS_BACKTEST_MODE = True`
2. 配置回测参数
3. 运行策略脚本
4. 查看回测结果

### 实盘模式
1. 设置 `IS_BACKTEST_MODE = False`
2. 配置实盘参数
3. 确保iQuant环境正常
4. 启动策略监控

### 数据库管理
- 使用 `database/init_database_only.py` 初始化数据库
- 使用 `database/check_error_logs.py` 检查错误日志
- 数据库文件：`database/gytrading2.db`

## 开发环境

### 依赖库
- sqlite3 (数据库)
- datetime (时间处理)
- json (数据序列化)
- pandas (数据分析，可选)
- talib (技术指标，可选)

### 平台要求
- iQuant量化交易平台
- Python 3.x
- Windows环境

## 注意事项

1. **风险提示**：量化交易存在风险，请谨慎使用
2. **测试建议**：实盘前请充分回测和小额测试
3. **监控重要**：实盘运行时请密切监控策略表现
4. **参数调优**：根据市场情况适时调整策略参数

## 联系方式

如有问题请查看 `/docs/` 目录下的相关文档，或检查 `/archive/` 目录下的历史记录。
