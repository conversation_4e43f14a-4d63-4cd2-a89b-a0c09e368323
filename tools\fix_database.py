#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库修复脚本
用于修复缺失的 trade_execution_log 等表
"""

import sqlite3
import os
import datetime

# 数据库路径
DATABASE_PATH = "gytrading2.db"

def create_missing_tables():
    """
    创建缺失的表
    """
    try:
        # 连接数据库
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        print("开始检查和创建缺失的表...")
        
        # 检查 trade_execution_log 表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='trade_execution_log'")
        if not cursor.fetchone():
            print("创建 trade_execution_log 表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trade_execution_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_time TEXT NOT NULL,             -- 交易时间
                    trade_type TEXT NOT NULL,             -- 交易类型：BUY/SELL
                    stock_code TEXT NOT NULL,             -- 股票代码
                    shares INTEGER NOT NULL,              -- 股数
                    price REAL,                           -- 价格
                    amount REAL,                          -- 金额
                    fees REAL,                            -- 总费用
                    order_id TEXT,                        -- 订单ID
                    order_uuid TEXT,                      -- 订单UUID（用于回调匹配）
                    status TEXT NOT NULL,                 -- 状态：SUCCESS/FAILED/PENDING/CANCELLED
                    error_message TEXT,                   -- 错误信息
                    created_time TEXT NOT NULL           -- 创建时间
                )
            """)
            print("trade_execution_log 表创建成功")
        else:
            print("trade_execution_log 表已存在")
        
        # 检查 trade_fee_details 表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='trade_fee_details'")
        if not cursor.fetchone():
            print("创建 trade_fee_details 表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trade_fee_details (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    execution_log_id INTEGER,            -- 关联trade_execution_log.id
                    order_uuid TEXT NOT NULL,            -- 订单UUID
                    commission REAL DEFAULT 0,           -- 佣金
                    stamp_tax REAL DEFAULT 0,            -- 印花税
                    transfer_fee REAL DEFAULT 0,         -- 过户费
                    other_fees REAL DEFAULT 0,           -- 其他费用
                    total_fees REAL DEFAULT 0,           -- 总费用
                    net_amount REAL DEFAULT 0,           -- 净金额
                    created_time TEXT NOT NULL,          -- 创建时间
                    FOREIGN KEY (execution_log_id) REFERENCES trade_execution_log (id)
                )
            """)
            print("trade_fee_details 表创建成功")
        else:
            print("trade_fee_details 表已存在")
        
        # 检查 order_status_history 表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='order_status_history'")
        if not cursor.fetchone():
            print("创建 order_status_history 表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS order_status_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_uuid TEXT NOT NULL,            -- 订单UUID
                    order_id TEXT,                       -- 订单ID
                    stock_code TEXT,                     -- 股票代码
                    order_status INTEGER NOT NULL,       -- 订单状态码
                    status_desc TEXT,                    -- 状态描述
                    volume_traded INTEGER DEFAULT 0,    -- 已成交量
                    volume_total INTEGER DEFAULT 0,     -- 总委托量
                    callback_time TEXT NOT NULL,        -- 回调时间
                    created_time TEXT NOT NULL          -- 记录创建时间
                )
            """)
            print("order_status_history 表创建成功")
        else:
            print("order_status_history 表已存在")
        
        # 创建索引
        print("创建索引...")
        
        # 交易执行记录表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_execution_log_time ON trade_execution_log(trade_time)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_execution_log_uuid ON trade_execution_log(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_execution_log_status ON trade_execution_log(status)")
        
        # 交易费用明细表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_fee_details_uuid ON trade_fee_details(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_fee_details_execution_id ON trade_fee_details(execution_log_id)")
        
        # 订单状态历史表索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_order_status_history_uuid ON order_status_history(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_order_status_history_time ON order_status_history(callback_time)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_order_status_history_status ON order_status_history(order_status)")
        
        print("索引创建成功")
        
        # 提交更改
        connection.commit()
        print("数据库修复完成！")
        
        # 验证表是否创建成功
        print("\n验证表结构...")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        print("当前数据库中的表：")
        for table in tables:
            print(f"  - {table[0]}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"数据库修复失败：{str(e)}")
        return False
    
    return True

def main():
    """
    主函数
    """
    print("=" * 50)
    print("数据库修复脚本")
    print("=" * 50)
    
    if not os.path.exists(DATABASE_PATH):
        print(f"错误：数据库文件 {DATABASE_PATH} 不存在")
        return
    
    print(f"数据库文件：{DATABASE_PATH}")
    
    if create_missing_tables():
        print("\n✅ 数据库修复成功！")
        print("现在可以重新运行交易策略程序了。")
    else:
        print("\n❌ 数据库修复失败！")

if __name__ == "__main__":
    main()
