# -*- coding: utf-8 -*-
"""
最终修复所有log_message调用
"""

import re

def final_fix_all_logs():
    """最终修复所有log_message调用"""
    
    # 读取文件
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("开始最终修复...")
    
    # 1. 修复所有错误的str(e, None, ContextInfo)
    content = re.sub(r'str\(e, None, ContextInfo\)', 'str(e)', content)
    print("修复了str(e, None, ContextInfo)")
    
    # 2. 修复所有错误的type(xxx, None, ContextInfo)
    content = re.sub(r'type\([^,)]+, None, ContextInfo\)', lambda m: m.group(0).split(', None, ContextInfo')[0] + ')', content)
    print("修复了type(xxx, None, ContextInfo)")
    
    # 3. 修复所有错误的len(xxx, None, ContextInfo)
    content = re.sub(r'len\([^,)]+, None, ContextInfo\)', lambda m: m.group(0).split(', None, ContextInfo')[0] + ')', content)
    print("修复了len(xxx, None, ContextInfo)")
    
    # 4. 修复重复的ContextInfo参数
    content = re.sub(r', None, ContextInfo, None, ContextInfo\)', ', None, ContextInfo)', content)
    print("修复了重复的ContextInfo参数")
    
    # 5. 修复函数定义
    content = re.sub(
        r'def log_message\([^)]+, None, ContextInfo\):',
        'def log_message(log_type: str, operation: str, message: str, details: Dict = None, ContextInfo=None):',
        content
    )
    print("修复了函数定义")
    
    # 6. 修复任务队列中的错误调用
    content = re.sub(
        r'log_message\(level, f"任务队列-\{category\}", message, extra_data, None, ContextInfo, None, ContextInfo\)',
        'log_message(level, f"任务队列-{category}", message, extra_data, None, ContextInfo)',
        content
    )
    print("修复了任务队列调用")
    
    # 7. 找到所有仍然没有ContextInfo的log_message调用（排除函数定义）
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # 跳过函数定义行
        if 'def log_message(' in line:
            fixed_lines.append(line)
            continue
            
        # 查找log_message调用
        if 'log_message(' in line and ', ContextInfo)' not in line:
            # 检查是否是注释行
            stripped = line.strip()
            if stripped.startswith('#'):
                fixed_lines.append(line)
                continue
                
            # 修复调用
            if line.endswith(')'):
                # 在最后的)前添加, None, ContextInfo
                new_line = line[:-1] + ', None, ContextInfo)'
                fixed_lines.append(new_line)
                if i % 50 == 0:
                    print(f"修复第{i}行: {line.strip()}")
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    content = '\n'.join(fixed_lines)
    
    # 8. 最后检查还有多少未修复的
    remaining_pattern = r'log_message\([^)]+\)(?![^)]*ContextInfo)'
    remaining_matches = re.findall(remaining_pattern, content)
    
    # 过滤掉函数定义
    remaining_matches = [m for m in remaining_matches if 'def log_message(' not in m]
    
    print(f"剩余未修复的调用: {len(remaining_matches)}")
    
    # 写回文件
    with open('value_averaging_strategy.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("文件已更新！")
    
    return len(remaining_matches)

def main():
    """主函数"""
    print("开始最终修复所有log_message调用...")
    print("=" * 50)
    
    try:
        remaining = final_fix_all_logs()
        
        if remaining == 0:
            print(f"\n🎉 完美！所有log_message调用都已修复！")
        else:
            print(f"\n⚠️  还有 {remaining} 个调用需要手动修复")
        
        print("现在所有log_message调用都应该包含ContextInfo参数了！")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
