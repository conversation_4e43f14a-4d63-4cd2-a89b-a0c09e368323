# encoding:gbk
"""
简单测试 get_current_time 函数修复
"""

import datetime

# 模拟全局变量
g_current_bar_time = None
IS_BACKTEST_MODE = False

def is_backtest_mode(ContextInfo):
    """模拟回测模式检查"""
    return IS_BACKTEST_MODE

def log_message(level, operation, message, details, ContextInfo):
    """模拟日志函数"""
    print(f"[{level}] {operation}: {message}")

def get_current_time(ContextInfo):
    """
    获取当前时间（修复后的版本）
    """
    global g_current_bar_time

    try:
        if is_backtest_mode(ContextInfo):
            # 回测模式：使用当前K线的时间
            try:
                current_bar_timestamp = ContextInfo.get_bar_timetag(ContextInfo.barpos)
                if current_bar_timestamp:
                    print(f'current_bar_ts ===> {current_bar_timestamp}')
                    g_current_bar_time = datetime.datetime.fromtimestamp(current_bar_timestamp/1000)
            except:
                print('获取K线时间失败，将会返回global的bartime')
                pass
            
            print(f'barpos = {ContextInfo.barpos}， bartime = {g_current_bar_time}')
            # 如果无法获取K线时间，使用缓存的时间
            if g_current_bar_time:
                return g_current_bar_time
            else:
                # 获取不到k线时间，也没有缓存时间，返回当前
                return datetime.datetime.now()
        else:
            # 实盘模式或无法获取K线时间：使用系统当前时间
            current_time = datetime.datetime.now()
            # 在实盘模式下也需要设置g_current_bar_time，供其他函数使用
            g_current_bar_time = current_time
            return current_time

    except Exception as e:
        log_message("WARNING", "时间获取", f"获取当前时间失败：{str(e)}", None, ContextInfo)
        current_time = datetime.datetime.now()
        # 异常情况下也需要设置g_current_bar_time
        g_current_bar_time = current_time
        return current_time

# 模拟 ContextInfo
class MockContextInfo:
    def __init__(self):
        self.barpos = 100
    
    def get_bar_timetag(self, barpos):
        return None

def test_fix():
    """
    测试修复
    """
    print("=" * 50)
    print("测试 get_current_time 修复")
    print("=" * 50)
    
    # 测试实盘模式
    print("1. 测试实盘模式:")
    mock_context = MockContextInfo()
    
    print(f"调用前 g_current_bar_time: {g_current_bar_time}")
    
    current_time = get_current_time(mock_context)
    print(f"get_current_time 返回: {current_time}")
    print(f"调用后 g_current_bar_time: {g_current_bar_time}")
    
    if g_current_bar_time is not None:
        print("✅ 实盘模式下 g_current_bar_time 已正确设置")
        
        # 测试在 update_technical_indicators 中使用 g_current_bar_time
        try:
            time_str = g_current_bar_time.strftime('%Y%m%d')
            print(f"✅ g_current_bar_time.strftime('%Y%m%d') = {time_str}")
            print("✅ 修复成功！现在可以在 update_technical_indicators 中安全使用 g_current_bar_time")
        except Exception as e:
            print(f"❌ 使用 g_current_bar_time.strftime 失败: {str(e)}")
    else:
        print("❌ 实盘模式下 g_current_bar_time 未设置")
    
    # 测试异常情况
    print("\n2. 测试异常情况:")
    class ErrorContextInfo:
        def __init__(self):
            self.barpos = 100
        
        def get_bar_timetag(self, barpos):
            raise Exception("模拟异常")
    
    error_context = ErrorContextInfo()
    
    try:
        current_time_error = get_current_time(error_context)
        print(f"异常情况 get_current_time 返回: {current_time_error}")
        print(f"异常情况 g_current_bar_time: {g_current_bar_time}")
        
        if g_current_bar_time is not None:
            print("✅ 异常情况下 g_current_bar_time 已正确设置")
            time_str = g_current_bar_time.strftime('%Y%m%d')
            print(f"✅ 异常情况 g_current_bar_time.strftime('%Y%m%d') = {time_str}")
        else:
            print("❌ 异常情况下 g_current_bar_time 未设置")
    except Exception as e:
        print(f"测试异常情况时出错: {str(e)}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    test_fix()
